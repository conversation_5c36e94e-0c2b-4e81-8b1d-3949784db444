"""Data derived from the Unicode character database (UCD).

This file was generated from pyunormalize/tools/generate_unicode.py
"""

_UNICODE_VERSION = "16.0.0"

# Dictionary mapping characters to their canonical decompositions,
# not including Hangul syllables
_DECOMP_BY_CHARACTER = {
    0x000A0: ["<noBreak>", 0x0020],
    0x000A8: ["<compat>", 0x0020, 0x0308],
    0x000AA: ["<super>", 0x0061],
    0x000AF: ["<compat>", 0x0020, 0x0304],
    0x000B2: ["<super>", 0x0032],
    0x000B3: ["<super>", 0x0033],
    0x000B4: ["<compat>", 0x0020, 0x0301],
    0x000B5: ["<compat>", 0x03BC],
    0x000B8: ["<compat>", 0x0020, 0x0327],
    0x000B9: ["<super>", 0x0031],
    0x000BA: ["<super>", 0x006F],
    0x000BC: ["<fraction>", 0x0031, 0x2044, 0x0034],
    0x000BD: ["<fraction>", 0x0031, 0x2044, 0x0032],
    0x000BE: ["<fraction>", 0x0033, 0x2044, 0x0034],
    0x000C0: [0x0041, 0x0300],
    0x000C1: [0x0041, 0x0301],
    0x000C2: [0x0041, 0x0302],
    0x000C3: [0x0041, 0x0303],
    0x000C4: [0x0041, 0x0308],
    0x000C5: [0x0041, 0x030A],
    0x000C7: [0x0043, 0x0327],
    0x000C8: [0x0045, 0x0300],
    0x000C9: [0x0045, 0x0301],
    0x000CA: [0x0045, 0x0302],
    0x000CB: [0x0045, 0x0308],
    0x000CC: [0x0049, 0x0300],
    0x000CD: [0x0049, 0x0301],
    0x000CE: [0x0049, 0x0302],
    0x000CF: [0x0049, 0x0308],
    0x000D1: [0x004E, 0x0303],
    0x000D2: [0x004F, 0x0300],
    0x000D3: [0x004F, 0x0301],
    0x000D4: [0x004F, 0x0302],
    0x000D5: [0x004F, 0x0303],
    0x000D6: [0x004F, 0x0308],
    0x000D9: [0x0055, 0x0300],
    0x000DA: [0x0055, 0x0301],
    0x000DB: [0x0055, 0x0302],
    0x000DC: [0x0055, 0x0308],
    0x000DD: [0x0059, 0x0301],
    0x000E0: [0x0061, 0x0300],
    0x000E1: [0x0061, 0x0301],
    0x000E2: [0x0061, 0x0302],
    0x000E3: [0x0061, 0x0303],
    0x000E4: [0x0061, 0x0308],
    0x000E5: [0x0061, 0x030A],
    0x000E7: [0x0063, 0x0327],
    0x000E8: [0x0065, 0x0300],
    0x000E9: [0x0065, 0x0301],
    0x000EA: [0x0065, 0x0302],
    0x000EB: [0x0065, 0x0308],
    0x000EC: [0x0069, 0x0300],
    0x000ED: [0x0069, 0x0301],
    0x000EE: [0x0069, 0x0302],
    0x000EF: [0x0069, 0x0308],
    0x000F1: [0x006E, 0x0303],
    0x000F2: [0x006F, 0x0300],
    0x000F3: [0x006F, 0x0301],
    0x000F4: [0x006F, 0x0302],
    0x000F5: [0x006F, 0x0303],
    0x000F6: [0x006F, 0x0308],
    0x000F9: [0x0075, 0x0300],
    0x000FA: [0x0075, 0x0301],
    0x000FB: [0x0075, 0x0302],
    0x000FC: [0x0075, 0x0308],
    0x000FD: [0x0079, 0x0301],
    0x000FF: [0x0079, 0x0308],
    0x00100: [0x0041, 0x0304],
    0x00101: [0x0061, 0x0304],
    0x00102: [0x0041, 0x0306],
    0x00103: [0x0061, 0x0306],
    0x00104: [0x0041, 0x0328],
    0x00105: [0x0061, 0x0328],
    0x00106: [0x0043, 0x0301],
    0x00107: [0x0063, 0x0301],
    0x00108: [0x0043, 0x0302],
    0x00109: [0x0063, 0x0302],
    0x0010A: [0x0043, 0x0307],
    0x0010B: [0x0063, 0x0307],
    0x0010C: [0x0043, 0x030C],
    0x0010D: [0x0063, 0x030C],
    0x0010E: [0x0044, 0x030C],
    0x0010F: [0x0064, 0x030C],
    0x00112: [0x0045, 0x0304],
    0x00113: [0x0065, 0x0304],
    0x00114: [0x0045, 0x0306],
    0x00115: [0x0065, 0x0306],
    0x00116: [0x0045, 0x0307],
    0x00117: [0x0065, 0x0307],
    0x00118: [0x0045, 0x0328],
    0x00119: [0x0065, 0x0328],
    0x0011A: [0x0045, 0x030C],
    0x0011B: [0x0065, 0x030C],
    0x0011C: [0x0047, 0x0302],
    0x0011D: [0x0067, 0x0302],
    0x0011E: [0x0047, 0x0306],
    0x0011F: [0x0067, 0x0306],
    0x00120: [0x0047, 0x0307],
    0x00121: [0x0067, 0x0307],
    0x00122: [0x0047, 0x0327],
    0x00123: [0x0067, 0x0327],
    0x00124: [0x0048, 0x0302],
    0x00125: [0x0068, 0x0302],
    0x00128: [0x0049, 0x0303],
    0x00129: [0x0069, 0x0303],
    0x0012A: [0x0049, 0x0304],
    0x0012B: [0x0069, 0x0304],
    0x0012C: [0x0049, 0x0306],
    0x0012D: [0x0069, 0x0306],
    0x0012E: [0x0049, 0x0328],
    0x0012F: [0x0069, 0x0328],
    0x00130: [0x0049, 0x0307],
    0x00132: ["<compat>", 0x0049, 0x004A],
    0x00133: ["<compat>", 0x0069, 0x006A],
    0x00134: [0x004A, 0x0302],
    0x00135: [0x006A, 0x0302],
    0x00136: [0x004B, 0x0327],
    0x00137: [0x006B, 0x0327],
    0x00139: [0x004C, 0x0301],
    0x0013A: [0x006C, 0x0301],
    0x0013B: [0x004C, 0x0327],
    0x0013C: [0x006C, 0x0327],
    0x0013D: [0x004C, 0x030C],
    0x0013E: [0x006C, 0x030C],
    0x0013F: ["<compat>", 0x004C, 0x00B7],
    0x00140: ["<compat>", 0x006C, 0x00B7],
    0x00143: [0x004E, 0x0301],
    0x00144: [0x006E, 0x0301],
    0x00145: [0x004E, 0x0327],
    0x00146: [0x006E, 0x0327],
    0x00147: [0x004E, 0x030C],
    0x00148: [0x006E, 0x030C],
    0x00149: ["<compat>", 0x02BC, 0x006E],
    0x0014C: [0x004F, 0x0304],
    0x0014D: [0x006F, 0x0304],
    0x0014E: [0x004F, 0x0306],
    0x0014F: [0x006F, 0x0306],
    0x00150: [0x004F, 0x030B],
    0x00151: [0x006F, 0x030B],
    0x00154: [0x0052, 0x0301],
    0x00155: [0x0072, 0x0301],
    0x00156: [0x0052, 0x0327],
    0x00157: [0x0072, 0x0327],
    0x00158: [0x0052, 0x030C],
    0x00159: [0x0072, 0x030C],
    0x0015A: [0x0053, 0x0301],
    0x0015B: [0x0073, 0x0301],
    0x0015C: [0x0053, 0x0302],
    0x0015D: [0x0073, 0x0302],
    0x0015E: [0x0053, 0x0327],
    0x0015F: [0x0073, 0x0327],
    0x00160: [0x0053, 0x030C],
    0x00161: [0x0073, 0x030C],
    0x00162: [0x0054, 0x0327],
    0x00163: [0x0074, 0x0327],
    0x00164: [0x0054, 0x030C],
    0x00165: [0x0074, 0x030C],
    0x00168: [0x0055, 0x0303],
    0x00169: [0x0075, 0x0303],
    0x0016A: [0x0055, 0x0304],
    0x0016B: [0x0075, 0x0304],
    0x0016C: [0x0055, 0x0306],
    0x0016D: [0x0075, 0x0306],
    0x0016E: [0x0055, 0x030A],
    0x0016F: [0x0075, 0x030A],
    0x00170: [0x0055, 0x030B],
    0x00171: [0x0075, 0x030B],
    0x00172: [0x0055, 0x0328],
    0x00173: [0x0075, 0x0328],
    0x00174: [0x0057, 0x0302],
    0x00175: [0x0077, 0x0302],
    0x00176: [0x0059, 0x0302],
    0x00177: [0x0079, 0x0302],
    0x00178: [0x0059, 0x0308],
    0x00179: [0x005A, 0x0301],
    0x0017A: [0x007A, 0x0301],
    0x0017B: [0x005A, 0x0307],
    0x0017C: [0x007A, 0x0307],
    0x0017D: [0x005A, 0x030C],
    0x0017E: [0x007A, 0x030C],
    0x0017F: ["<compat>", 0x0073],
    0x001A0: [0x004F, 0x031B],
    0x001A1: [0x006F, 0x031B],
    0x001AF: [0x0055, 0x031B],
    0x001B0: [0x0075, 0x031B],
    0x001C4: ["<compat>", 0x0044, 0x017D],
    0x001C5: ["<compat>", 0x0044, 0x017E],
    0x001C6: ["<compat>", 0x0064, 0x017E],
    0x001C7: ["<compat>", 0x004C, 0x004A],
    0x001C8: ["<compat>", 0x004C, 0x006A],
    0x001C9: ["<compat>", 0x006C, 0x006A],
    0x001CA: ["<compat>", 0x004E, 0x004A],
    0x001CB: ["<compat>", 0x004E, 0x006A],
    0x001CC: ["<compat>", 0x006E, 0x006A],
    0x001CD: [0x0041, 0x030C],
    0x001CE: [0x0061, 0x030C],
    0x001CF: [0x0049, 0x030C],
    0x001D0: [0x0069, 0x030C],
    0x001D1: [0x004F, 0x030C],
    0x001D2: [0x006F, 0x030C],
    0x001D3: [0x0055, 0x030C],
    0x001D4: [0x0075, 0x030C],
    0x001D5: [0x00DC, 0x0304],
    0x001D6: [0x00FC, 0x0304],
    0x001D7: [0x00DC, 0x0301],
    0x001D8: [0x00FC, 0x0301],
    0x001D9: [0x00DC, 0x030C],
    0x001DA: [0x00FC, 0x030C],
    0x001DB: [0x00DC, 0x0300],
    0x001DC: [0x00FC, 0x0300],
    0x001DE: [0x00C4, 0x0304],
    0x001DF: [0x00E4, 0x0304],
    0x001E0: [0x0226, 0x0304],
    0x001E1: [0x0227, 0x0304],
    0x001E2: [0x00C6, 0x0304],
    0x001E3: [0x00E6, 0x0304],
    0x001E6: [0x0047, 0x030C],
    0x001E7: [0x0067, 0x030C],
    0x001E8: [0x004B, 0x030C],
    0x001E9: [0x006B, 0x030C],
    0x001EA: [0x004F, 0x0328],
    0x001EB: [0x006F, 0x0328],
    0x001EC: [0x01EA, 0x0304],
    0x001ED: [0x01EB, 0x0304],
    0x001EE: [0x01B7, 0x030C],
    0x001EF: [0x0292, 0x030C],
    0x001F0: [0x006A, 0x030C],
    0x001F1: ["<compat>", 0x0044, 0x005A],
    0x001F2: ["<compat>", 0x0044, 0x007A],
    0x001F3: ["<compat>", 0x0064, 0x007A],
    0x001F4: [0x0047, 0x0301],
    0x001F5: [0x0067, 0x0301],
    0x001F8: [0x004E, 0x0300],
    0x001F9: [0x006E, 0x0300],
    0x001FA: [0x00C5, 0x0301],
    0x001FB: [0x00E5, 0x0301],
    0x001FC: [0x00C6, 0x0301],
    0x001FD: [0x00E6, 0x0301],
    0x001FE: [0x00D8, 0x0301],
    0x001FF: [0x00F8, 0x0301],
    0x00200: [0x0041, 0x030F],
    0x00201: [0x0061, 0x030F],
    0x00202: [0x0041, 0x0311],
    0x00203: [0x0061, 0x0311],
    0x00204: [0x0045, 0x030F],
    0x00205: [0x0065, 0x030F],
    0x00206: [0x0045, 0x0311],
    0x00207: [0x0065, 0x0311],
    0x00208: [0x0049, 0x030F],
    0x00209: [0x0069, 0x030F],
    0x0020A: [0x0049, 0x0311],
    0x0020B: [0x0069, 0x0311],
    0x0020C: [0x004F, 0x030F],
    0x0020D: [0x006F, 0x030F],
    0x0020E: [0x004F, 0x0311],
    0x0020F: [0x006F, 0x0311],
    0x00210: [0x0052, 0x030F],
    0x00211: [0x0072, 0x030F],
    0x00212: [0x0052, 0x0311],
    0x00213: [0x0072, 0x0311],
    0x00214: [0x0055, 0x030F],
    0x00215: [0x0075, 0x030F],
    0x00216: [0x0055, 0x0311],
    0x00217: [0x0075, 0x0311],
    0x00218: [0x0053, 0x0326],
    0x00219: [0x0073, 0x0326],
    0x0021A: [0x0054, 0x0326],
    0x0021B: [0x0074, 0x0326],
    0x0021E: [0x0048, 0x030C],
    0x0021F: [0x0068, 0x030C],
    0x00226: [0x0041, 0x0307],
    0x00227: [0x0061, 0x0307],
    0x00228: [0x0045, 0x0327],
    0x00229: [0x0065, 0x0327],
    0x0022A: [0x00D6, 0x0304],
    0x0022B: [0x00F6, 0x0304],
    0x0022C: [0x00D5, 0x0304],
    0x0022D: [0x00F5, 0x0304],
    0x0022E: [0x004F, 0x0307],
    0x0022F: [0x006F, 0x0307],
    0x00230: [0x022E, 0x0304],
    0x00231: [0x022F, 0x0304],
    0x00232: [0x0059, 0x0304],
    0x00233: [0x0079, 0x0304],
    0x002B0: ["<super>", 0x0068],
    0x002B1: ["<super>", 0x0266],
    0x002B2: ["<super>", 0x006A],
    0x002B3: ["<super>", 0x0072],
    0x002B4: ["<super>", 0x0279],
    0x002B5: ["<super>", 0x027B],
    0x002B6: ["<super>", 0x0281],
    0x002B7: ["<super>", 0x0077],
    0x002B8: ["<super>", 0x0079],
    0x002D8: ["<compat>", 0x0020, 0x0306],
    0x002D9: ["<compat>", 0x0020, 0x0307],
    0x002DA: ["<compat>", 0x0020, 0x030A],
    0x002DB: ["<compat>", 0x0020, 0x0328],
    0x002DC: ["<compat>", 0x0020, 0x0303],
    0x002DD: ["<compat>", 0x0020, 0x030B],
    0x002E0: ["<super>", 0x0263],
    0x002E1: ["<super>", 0x006C],
    0x002E2: ["<super>", 0x0073],
    0x002E3: ["<super>", 0x0078],
    0x002E4: ["<super>", 0x0295],
    0x00340: [0x0300],
    0x00341: [0x0301],
    0x00343: [0x0313],
    0x00344: [0x0308, 0x0301],
    0x00374: [0x02B9],
    0x0037A: ["<compat>", 0x0020, 0x0345],
    0x0037E: [0x003B],
    0x00384: ["<compat>", 0x0020, 0x0301],
    0x00385: [0x00A8, 0x0301],
    0x00386: [0x0391, 0x0301],
    0x00387: [0x00B7],
    0x00388: [0x0395, 0x0301],
    0x00389: [0x0397, 0x0301],
    0x0038A: [0x0399, 0x0301],
    0x0038C: [0x039F, 0x0301],
    0x0038E: [0x03A5, 0x0301],
    0x0038F: [0x03A9, 0x0301],
    0x00390: [0x03CA, 0x0301],
    0x003AA: [0x0399, 0x0308],
    0x003AB: [0x03A5, 0x0308],
    0x003AC: [0x03B1, 0x0301],
    0x003AD: [0x03B5, 0x0301],
    0x003AE: [0x03B7, 0x0301],
    0x003AF: [0x03B9, 0x0301],
    0x003B0: [0x03CB, 0x0301],
    0x003CA: [0x03B9, 0x0308],
    0x003CB: [0x03C5, 0x0308],
    0x003CC: [0x03BF, 0x0301],
    0x003CD: [0x03C5, 0x0301],
    0x003CE: [0x03C9, 0x0301],
    0x003D0: ["<compat>", 0x03B2],
    0x003D1: ["<compat>", 0x03B8],
    0x003D2: ["<compat>", 0x03A5],
    0x003D3: [0x03D2, 0x0301],
    0x003D4: [0x03D2, 0x0308],
    0x003D5: ["<compat>", 0x03C6],
    0x003D6: ["<compat>", 0x03C0],
    0x003F0: ["<compat>", 0x03BA],
    0x003F1: ["<compat>", 0x03C1],
    0x003F2: ["<compat>", 0x03C2],
    0x003F4: ["<compat>", 0x0398],
    0x003F5: ["<compat>", 0x03B5],
    0x003F9: ["<compat>", 0x03A3],
    0x00400: [0x0415, 0x0300],
    0x00401: [0x0415, 0x0308],
    0x00403: [0x0413, 0x0301],
    0x00407: [0x0406, 0x0308],
    0x0040C: [0x041A, 0x0301],
    0x0040D: [0x0418, 0x0300],
    0x0040E: [0x0423, 0x0306],
    0x00419: [0x0418, 0x0306],
    0x00439: [0x0438, 0x0306],
    0x00450: [0x0435, 0x0300],
    0x00451: [0x0435, 0x0308],
    0x00453: [0x0433, 0x0301],
    0x00457: [0x0456, 0x0308],
    0x0045C: [0x043A, 0x0301],
    0x0045D: [0x0438, 0x0300],
    0x0045E: [0x0443, 0x0306],
    0x00476: [0x0474, 0x030F],
    0x00477: [0x0475, 0x030F],
    0x004C1: [0x0416, 0x0306],
    0x004C2: [0x0436, 0x0306],
    0x004D0: [0x0410, 0x0306],
    0x004D1: [0x0430, 0x0306],
    0x004D2: [0x0410, 0x0308],
    0x004D3: [0x0430, 0x0308],
    0x004D6: [0x0415, 0x0306],
    0x004D7: [0x0435, 0x0306],
    0x004DA: [0x04D8, 0x0308],
    0x004DB: [0x04D9, 0x0308],
    0x004DC: [0x0416, 0x0308],
    0x004DD: [0x0436, 0x0308],
    0x004DE: [0x0417, 0x0308],
    0x004DF: [0x0437, 0x0308],
    0x004E2: [0x0418, 0x0304],
    0x004E3: [0x0438, 0x0304],
    0x004E4: [0x0418, 0x0308],
    0x004E5: [0x0438, 0x0308],
    0x004E6: [0x041E, 0x0308],
    0x004E7: [0x043E, 0x0308],
    0x004EA: [0x04E8, 0x0308],
    0x004EB: [0x04E9, 0x0308],
    0x004EC: [0x042D, 0x0308],
    0x004ED: [0x044D, 0x0308],
    0x004EE: [0x0423, 0x0304],
    0x004EF: [0x0443, 0x0304],
    0x004F0: [0x0423, 0x0308],
    0x004F1: [0x0443, 0x0308],
    0x004F2: [0x0423, 0x030B],
    0x004F3: [0x0443, 0x030B],
    0x004F4: [0x0427, 0x0308],
    0x004F5: [0x0447, 0x0308],
    0x004F8: [0x042B, 0x0308],
    0x004F9: [0x044B, 0x0308],
    0x00587: ["<compat>", 0x0565, 0x0582],
    0x00622: [0x0627, 0x0653],
    0x00623: [0x0627, 0x0654],
    0x00624: [0x0648, 0x0654],
    0x00625: [0x0627, 0x0655],
    0x00626: [0x064A, 0x0654],
    0x00675: ["<compat>", 0x0627, 0x0674],
    0x00676: ["<compat>", 0x0648, 0x0674],
    0x00677: ["<compat>", 0x06C7, 0x0674],
    0x00678: ["<compat>", 0x064A, 0x0674],
    0x006C0: [0x06D5, 0x0654],
    0x006C2: [0x06C1, 0x0654],
    0x006D3: [0x06D2, 0x0654],
    0x00929: [0x0928, 0x093C],
    0x00931: [0x0930, 0x093C],
    0x00934: [0x0933, 0x093C],
    0x00958: [0x0915, 0x093C],
    0x00959: [0x0916, 0x093C],
    0x0095A: [0x0917, 0x093C],
    0x0095B: [0x091C, 0x093C],
    0x0095C: [0x0921, 0x093C],
    0x0095D: [0x0922, 0x093C],
    0x0095E: [0x092B, 0x093C],
    0x0095F: [0x092F, 0x093C],
    0x009CB: [0x09C7, 0x09BE],
    0x009CC: [0x09C7, 0x09D7],
    0x009DC: [0x09A1, 0x09BC],
    0x009DD: [0x09A2, 0x09BC],
    0x009DF: [0x09AF, 0x09BC],
    0x00A33: [0x0A32, 0x0A3C],
    0x00A36: [0x0A38, 0x0A3C],
    0x00A59: [0x0A16, 0x0A3C],
    0x00A5A: [0x0A17, 0x0A3C],
    0x00A5B: [0x0A1C, 0x0A3C],
    0x00A5E: [0x0A2B, 0x0A3C],
    0x00B48: [0x0B47, 0x0B56],
    0x00B4B: [0x0B47, 0x0B3E],
    0x00B4C: [0x0B47, 0x0B57],
    0x00B5C: [0x0B21, 0x0B3C],
    0x00B5D: [0x0B22, 0x0B3C],
    0x00B94: [0x0B92, 0x0BD7],
    0x00BCA: [0x0BC6, 0x0BBE],
    0x00BCB: [0x0BC7, 0x0BBE],
    0x00BCC: [0x0BC6, 0x0BD7],
    0x00C48: [0x0C46, 0x0C56],
    0x00CC0: [0x0CBF, 0x0CD5],
    0x00CC7: [0x0CC6, 0x0CD5],
    0x00CC8: [0x0CC6, 0x0CD6],
    0x00CCA: [0x0CC6, 0x0CC2],
    0x00CCB: [0x0CCA, 0x0CD5],
    0x00D4A: [0x0D46, 0x0D3E],
    0x00D4B: [0x0D47, 0x0D3E],
    0x00D4C: [0x0D46, 0x0D57],
    0x00DDA: [0x0DD9, 0x0DCA],
    0x00DDC: [0x0DD9, 0x0DCF],
    0x00DDD: [0x0DDC, 0x0DCA],
    0x00DDE: [0x0DD9, 0x0DDF],
    0x00E33: ["<compat>", 0x0E4D, 0x0E32],
    0x00EB3: ["<compat>", 0x0ECD, 0x0EB2],
    0x00EDC: ["<compat>", 0x0EAB, 0x0E99],
    0x00EDD: ["<compat>", 0x0EAB, 0x0EA1],
    0x00F0C: ["<noBreak>", 0x0F0B],
    0x00F43: [0x0F42, 0x0FB7],
    0x00F4D: [0x0F4C, 0x0FB7],
    0x00F52: [0x0F51, 0x0FB7],
    0x00F57: [0x0F56, 0x0FB7],
    0x00F5C: [0x0F5B, 0x0FB7],
    0x00F69: [0x0F40, 0x0FB5],
    0x00F73: [0x0F71, 0x0F72],
    0x00F75: [0x0F71, 0x0F74],
    0x00F76: [0x0FB2, 0x0F80],
    0x00F77: ["<compat>", 0x0FB2, 0x0F81],
    0x00F78: [0x0FB3, 0x0F80],
    0x00F79: ["<compat>", 0x0FB3, 0x0F81],
    0x00F81: [0x0F71, 0x0F80],
    0x00F93: [0x0F92, 0x0FB7],
    0x00F9D: [0x0F9C, 0x0FB7],
    0x00FA2: [0x0FA1, 0x0FB7],
    0x00FA7: [0x0FA6, 0x0FB7],
    0x00FAC: [0x0FAB, 0x0FB7],
    0x00FB9: [0x0F90, 0x0FB5],
    0x01026: [0x1025, 0x102E],
    0x010FC: ["<super>", 0x10DC],
    0x01B06: [0x1B05, 0x1B35],
    0x01B08: [0x1B07, 0x1B35],
    0x01B0A: [0x1B09, 0x1B35],
    0x01B0C: [0x1B0B, 0x1B35],
    0x01B0E: [0x1B0D, 0x1B35],
    0x01B12: [0x1B11, 0x1B35],
    0x01B3B: [0x1B3A, 0x1B35],
    0x01B3D: [0x1B3C, 0x1B35],
    0x01B40: [0x1B3E, 0x1B35],
    0x01B41: [0x1B3F, 0x1B35],
    0x01B43: [0x1B42, 0x1B35],
    0x01D2C: ["<super>", 0x0041],
    0x01D2D: ["<super>", 0x00C6],
    0x01D2E: ["<super>", 0x0042],
    0x01D30: ["<super>", 0x0044],
    0x01D31: ["<super>", 0x0045],
    0x01D32: ["<super>", 0x018E],
    0x01D33: ["<super>", 0x0047],
    0x01D34: ["<super>", 0x0048],
    0x01D35: ["<super>", 0x0049],
    0x01D36: ["<super>", 0x004A],
    0x01D37: ["<super>", 0x004B],
    0x01D38: ["<super>", 0x004C],
    0x01D39: ["<super>", 0x004D],
    0x01D3A: ["<super>", 0x004E],
    0x01D3C: ["<super>", 0x004F],
    0x01D3D: ["<super>", 0x0222],
    0x01D3E: ["<super>", 0x0050],
    0x01D3F: ["<super>", 0x0052],
    0x01D40: ["<super>", 0x0054],
    0x01D41: ["<super>", 0x0055],
    0x01D42: ["<super>", 0x0057],
    0x01D43: ["<super>", 0x0061],
    0x01D44: ["<super>", 0x0250],
    0x01D45: ["<super>", 0x0251],
    0x01D46: ["<super>", 0x1D02],
    0x01D47: ["<super>", 0x0062],
    0x01D48: ["<super>", 0x0064],
    0x01D49: ["<super>", 0x0065],
    0x01D4A: ["<super>", 0x0259],
    0x01D4B: ["<super>", 0x025B],
    0x01D4C: ["<super>", 0x025C],
    0x01D4D: ["<super>", 0x0067],
    0x01D4F: ["<super>", 0x006B],
    0x01D50: ["<super>", 0x006D],
    0x01D51: ["<super>", 0x014B],
    0x01D52: ["<super>", 0x006F],
    0x01D53: ["<super>", 0x0254],
    0x01D54: ["<super>", 0x1D16],
    0x01D55: ["<super>", 0x1D17],
    0x01D56: ["<super>", 0x0070],
    0x01D57: ["<super>", 0x0074],
    0x01D58: ["<super>", 0x0075],
    0x01D59: ["<super>", 0x1D1D],
    0x01D5A: ["<super>", 0x026F],
    0x01D5B: ["<super>", 0x0076],
    0x01D5C: ["<super>", 0x1D25],
    0x01D5D: ["<super>", 0x03B2],
    0x01D5E: ["<super>", 0x03B3],
    0x01D5F: ["<super>", 0x03B4],
    0x01D60: ["<super>", 0x03C6],
    0x01D61: ["<super>", 0x03C7],
    0x01D62: ["<sub>", 0x0069],
    0x01D63: ["<sub>", 0x0072],
    0x01D64: ["<sub>", 0x0075],
    0x01D65: ["<sub>", 0x0076],
    0x01D66: ["<sub>", 0x03B2],
    0x01D67: ["<sub>", 0x03B3],
    0x01D68: ["<sub>", 0x03C1],
    0x01D69: ["<sub>", 0x03C6],
    0x01D6A: ["<sub>", 0x03C7],
    0x01D78: ["<super>", 0x043D],
    0x01D9B: ["<super>", 0x0252],
    0x01D9C: ["<super>", 0x0063],
    0x01D9D: ["<super>", 0x0255],
    0x01D9E: ["<super>", 0x00F0],
    0x01D9F: ["<super>", 0x025C],
    0x01DA0: ["<super>", 0x0066],
    0x01DA1: ["<super>", 0x025F],
    0x01DA2: ["<super>", 0x0261],
    0x01DA3: ["<super>", 0x0265],
    0x01DA4: ["<super>", 0x0268],
    0x01DA5: ["<super>", 0x0269],
    0x01DA6: ["<super>", 0x026A],
    0x01DA7: ["<super>", 0x1D7B],
    0x01DA8: ["<super>", 0x029D],
    0x01DA9: ["<super>", 0x026D],
    0x01DAA: ["<super>", 0x1D85],
    0x01DAB: ["<super>", 0x029F],
    0x01DAC: ["<super>", 0x0271],
    0x01DAD: ["<super>", 0x0270],
    0x01DAE: ["<super>", 0x0272],
    0x01DAF: ["<super>", 0x0273],
    0x01DB0: ["<super>", 0x0274],
    0x01DB1: ["<super>", 0x0275],
    0x01DB2: ["<super>", 0x0278],
    0x01DB3: ["<super>", 0x0282],
    0x01DB4: ["<super>", 0x0283],
    0x01DB5: ["<super>", 0x01AB],
    0x01DB6: ["<super>", 0x0289],
    0x01DB7: ["<super>", 0x028A],
    0x01DB8: ["<super>", 0x1D1C],
    0x01DB9: ["<super>", 0x028B],
    0x01DBA: ["<super>", 0x028C],
    0x01DBB: ["<super>", 0x007A],
    0x01DBC: ["<super>", 0x0290],
    0x01DBD: ["<super>", 0x0291],
    0x01DBE: ["<super>", 0x0292],
    0x01DBF: ["<super>", 0x03B8],
    0x01E00: [0x0041, 0x0325],
    0x01E01: [0x0061, 0x0325],
    0x01E02: [0x0042, 0x0307],
    0x01E03: [0x0062, 0x0307],
    0x01E04: [0x0042, 0x0323],
    0x01E05: [0x0062, 0x0323],
    0x01E06: [0x0042, 0x0331],
    0x01E07: [0x0062, 0x0331],
    0x01E08: [0x00C7, 0x0301],
    0x01E09: [0x00E7, 0x0301],
    0x01E0A: [0x0044, 0x0307],
    0x01E0B: [0x0064, 0x0307],
    0x01E0C: [0x0044, 0x0323],
    0x01E0D: [0x0064, 0x0323],
    0x01E0E: [0x0044, 0x0331],
    0x01E0F: [0x0064, 0x0331],
    0x01E10: [0x0044, 0x0327],
    0x01E11: [0x0064, 0x0327],
    0x01E12: [0x0044, 0x032D],
    0x01E13: [0x0064, 0x032D],
    0x01E14: [0x0112, 0x0300],
    0x01E15: [0x0113, 0x0300],
    0x01E16: [0x0112, 0x0301],
    0x01E17: [0x0113, 0x0301],
    0x01E18: [0x0045, 0x032D],
    0x01E19: [0x0065, 0x032D],
    0x01E1A: [0x0045, 0x0330],
    0x01E1B: [0x0065, 0x0330],
    0x01E1C: [0x0228, 0x0306],
    0x01E1D: [0x0229, 0x0306],
    0x01E1E: [0x0046, 0x0307],
    0x01E1F: [0x0066, 0x0307],
    0x01E20: [0x0047, 0x0304],
    0x01E21: [0x0067, 0x0304],
    0x01E22: [0x0048, 0x0307],
    0x01E23: [0x0068, 0x0307],
    0x01E24: [0x0048, 0x0323],
    0x01E25: [0x0068, 0x0323],
    0x01E26: [0x0048, 0x0308],
    0x01E27: [0x0068, 0x0308],
    0x01E28: [0x0048, 0x0327],
    0x01E29: [0x0068, 0x0327],
    0x01E2A: [0x0048, 0x032E],
    0x01E2B: [0x0068, 0x032E],
    0x01E2C: [0x0049, 0x0330],
    0x01E2D: [0x0069, 0x0330],
    0x01E2E: [0x00CF, 0x0301],
    0x01E2F: [0x00EF, 0x0301],
    0x01E30: [0x004B, 0x0301],
    0x01E31: [0x006B, 0x0301],
    0x01E32: [0x004B, 0x0323],
    0x01E33: [0x006B, 0x0323],
    0x01E34: [0x004B, 0x0331],
    0x01E35: [0x006B, 0x0331],
    0x01E36: [0x004C, 0x0323],
    0x01E37: [0x006C, 0x0323],
    0x01E38: [0x1E36, 0x0304],
    0x01E39: [0x1E37, 0x0304],
    0x01E3A: [0x004C, 0x0331],
    0x01E3B: [0x006C, 0x0331],
    0x01E3C: [0x004C, 0x032D],
    0x01E3D: [0x006C, 0x032D],
    0x01E3E: [0x004D, 0x0301],
    0x01E3F: [0x006D, 0x0301],
    0x01E40: [0x004D, 0x0307],
    0x01E41: [0x006D, 0x0307],
    0x01E42: [0x004D, 0x0323],
    0x01E43: [0x006D, 0x0323],
    0x01E44: [0x004E, 0x0307],
    0x01E45: [0x006E, 0x0307],
    0x01E46: [0x004E, 0x0323],
    0x01E47: [0x006E, 0x0323],
    0x01E48: [0x004E, 0x0331],
    0x01E49: [0x006E, 0x0331],
    0x01E4A: [0x004E, 0x032D],
    0x01E4B: [0x006E, 0x032D],
    0x01E4C: [0x00D5, 0x0301],
    0x01E4D: [0x00F5, 0x0301],
    0x01E4E: [0x00D5, 0x0308],
    0x01E4F: [0x00F5, 0x0308],
    0x01E50: [0x014C, 0x0300],
    0x01E51: [0x014D, 0x0300],
    0x01E52: [0x014C, 0x0301],
    0x01E53: [0x014D, 0x0301],
    0x01E54: [0x0050, 0x0301],
    0x01E55: [0x0070, 0x0301],
    0x01E56: [0x0050, 0x0307],
    0x01E57: [0x0070, 0x0307],
    0x01E58: [0x0052, 0x0307],
    0x01E59: [0x0072, 0x0307],
    0x01E5A: [0x0052, 0x0323],
    0x01E5B: [0x0072, 0x0323],
    0x01E5C: [0x1E5A, 0x0304],
    0x01E5D: [0x1E5B, 0x0304],
    0x01E5E: [0x0052, 0x0331],
    0x01E5F: [0x0072, 0x0331],
    0x01E60: [0x0053, 0x0307],
    0x01E61: [0x0073, 0x0307],
    0x01E62: [0x0053, 0x0323],
    0x01E63: [0x0073, 0x0323],
    0x01E64: [0x015A, 0x0307],
    0x01E65: [0x015B, 0x0307],
    0x01E66: [0x0160, 0x0307],
    0x01E67: [0x0161, 0x0307],
    0x01E68: [0x1E62, 0x0307],
    0x01E69: [0x1E63, 0x0307],
    0x01E6A: [0x0054, 0x0307],
    0x01E6B: [0x0074, 0x0307],
    0x01E6C: [0x0054, 0x0323],
    0x01E6D: [0x0074, 0x0323],
    0x01E6E: [0x0054, 0x0331],
    0x01E6F: [0x0074, 0x0331],
    0x01E70: [0x0054, 0x032D],
    0x01E71: [0x0074, 0x032D],
    0x01E72: [0x0055, 0x0324],
    0x01E73: [0x0075, 0x0324],
    0x01E74: [0x0055, 0x0330],
    0x01E75: [0x0075, 0x0330],
    0x01E76: [0x0055, 0x032D],
    0x01E77: [0x0075, 0x032D],
    0x01E78: [0x0168, 0x0301],
    0x01E79: [0x0169, 0x0301],
    0x01E7A: [0x016A, 0x0308],
    0x01E7B: [0x016B, 0x0308],
    0x01E7C: [0x0056, 0x0303],
    0x01E7D: [0x0076, 0x0303],
    0x01E7E: [0x0056, 0x0323],
    0x01E7F: [0x0076, 0x0323],
    0x01E80: [0x0057, 0x0300],
    0x01E81: [0x0077, 0x0300],
    0x01E82: [0x0057, 0x0301],
    0x01E83: [0x0077, 0x0301],
    0x01E84: [0x0057, 0x0308],
    0x01E85: [0x0077, 0x0308],
    0x01E86: [0x0057, 0x0307],
    0x01E87: [0x0077, 0x0307],
    0x01E88: [0x0057, 0x0323],
    0x01E89: [0x0077, 0x0323],
    0x01E8A: [0x0058, 0x0307],
    0x01E8B: [0x0078, 0x0307],
    0x01E8C: [0x0058, 0x0308],
    0x01E8D: [0x0078, 0x0308],
    0x01E8E: [0x0059, 0x0307],
    0x01E8F: [0x0079, 0x0307],
    0x01E90: [0x005A, 0x0302],
    0x01E91: [0x007A, 0x0302],
    0x01E92: [0x005A, 0x0323],
    0x01E93: [0x007A, 0x0323],
    0x01E94: [0x005A, 0x0331],
    0x01E95: [0x007A, 0x0331],
    0x01E96: [0x0068, 0x0331],
    0x01E97: [0x0074, 0x0308],
    0x01E98: [0x0077, 0x030A],
    0x01E99: [0x0079, 0x030A],
    0x01E9A: ["<compat>", 0x0061, 0x02BE],
    0x01E9B: [0x017F, 0x0307],
    0x01EA0: [0x0041, 0x0323],
    0x01EA1: [0x0061, 0x0323],
    0x01EA2: [0x0041, 0x0309],
    0x01EA3: [0x0061, 0x0309],
    0x01EA4: [0x00C2, 0x0301],
    0x01EA5: [0x00E2, 0x0301],
    0x01EA6: [0x00C2, 0x0300],
    0x01EA7: [0x00E2, 0x0300],
    0x01EA8: [0x00C2, 0x0309],
    0x01EA9: [0x00E2, 0x0309],
    0x01EAA: [0x00C2, 0x0303],
    0x01EAB: [0x00E2, 0x0303],
    0x01EAC: [0x1EA0, 0x0302],
    0x01EAD: [0x1EA1, 0x0302],
    0x01EAE: [0x0102, 0x0301],
    0x01EAF: [0x0103, 0x0301],
    0x01EB0: [0x0102, 0x0300],
    0x01EB1: [0x0103, 0x0300],
    0x01EB2: [0x0102, 0x0309],
    0x01EB3: [0x0103, 0x0309],
    0x01EB4: [0x0102, 0x0303],
    0x01EB5: [0x0103, 0x0303],
    0x01EB6: [0x1EA0, 0x0306],
    0x01EB7: [0x1EA1, 0x0306],
    0x01EB8: [0x0045, 0x0323],
    0x01EB9: [0x0065, 0x0323],
    0x01EBA: [0x0045, 0x0309],
    0x01EBB: [0x0065, 0x0309],
    0x01EBC: [0x0045, 0x0303],
    0x01EBD: [0x0065, 0x0303],
    0x01EBE: [0x00CA, 0x0301],
    0x01EBF: [0x00EA, 0x0301],
    0x01EC0: [0x00CA, 0x0300],
    0x01EC1: [0x00EA, 0x0300],
    0x01EC2: [0x00CA, 0x0309],
    0x01EC3: [0x00EA, 0x0309],
    0x01EC4: [0x00CA, 0x0303],
    0x01EC5: [0x00EA, 0x0303],
    0x01EC6: [0x1EB8, 0x0302],
    0x01EC7: [0x1EB9, 0x0302],
    0x01EC8: [0x0049, 0x0309],
    0x01EC9: [0x0069, 0x0309],
    0x01ECA: [0x0049, 0x0323],
    0x01ECB: [0x0069, 0x0323],
    0x01ECC: [0x004F, 0x0323],
    0x01ECD: [0x006F, 0x0323],
    0x01ECE: [0x004F, 0x0309],
    0x01ECF: [0x006F, 0x0309],
    0x01ED0: [0x00D4, 0x0301],
    0x01ED1: [0x00F4, 0x0301],
    0x01ED2: [0x00D4, 0x0300],
    0x01ED3: [0x00F4, 0x0300],
    0x01ED4: [0x00D4, 0x0309],
    0x01ED5: [0x00F4, 0x0309],
    0x01ED6: [0x00D4, 0x0303],
    0x01ED7: [0x00F4, 0x0303],
    0x01ED8: [0x1ECC, 0x0302],
    0x01ED9: [0x1ECD, 0x0302],
    0x01EDA: [0x01A0, 0x0301],
    0x01EDB: [0x01A1, 0x0301],
    0x01EDC: [0x01A0, 0x0300],
    0x01EDD: [0x01A1, 0x0300],
    0x01EDE: [0x01A0, 0x0309],
    0x01EDF: [0x01A1, 0x0309],
    0x01EE0: [0x01A0, 0x0303],
    0x01EE1: [0x01A1, 0x0303],
    0x01EE2: [0x01A0, 0x0323],
    0x01EE3: [0x01A1, 0x0323],
    0x01EE4: [0x0055, 0x0323],
    0x01EE5: [0x0075, 0x0323],
    0x01EE6: [0x0055, 0x0309],
    0x01EE7: [0x0075, 0x0309],
    0x01EE8: [0x01AF, 0x0301],
    0x01EE9: [0x01B0, 0x0301],
    0x01EEA: [0x01AF, 0x0300],
    0x01EEB: [0x01B0, 0x0300],
    0x01EEC: [0x01AF, 0x0309],
    0x01EED: [0x01B0, 0x0309],
    0x01EEE: [0x01AF, 0x0303],
    0x01EEF: [0x01B0, 0x0303],
    0x01EF0: [0x01AF, 0x0323],
    0x01EF1: [0x01B0, 0x0323],
    0x01EF2: [0x0059, 0x0300],
    0x01EF3: [0x0079, 0x0300],
    0x01EF4: [0x0059, 0x0323],
    0x01EF5: [0x0079, 0x0323],
    0x01EF6: [0x0059, 0x0309],
    0x01EF7: [0x0079, 0x0309],
    0x01EF8: [0x0059, 0x0303],
    0x01EF9: [0x0079, 0x0303],
    0x01F00: [0x03B1, 0x0313],
    0x01F01: [0x03B1, 0x0314],
    0x01F02: [0x1F00, 0x0300],
    0x01F03: [0x1F01, 0x0300],
    0x01F04: [0x1F00, 0x0301],
    0x01F05: [0x1F01, 0x0301],
    0x01F06: [0x1F00, 0x0342],
    0x01F07: [0x1F01, 0x0342],
    0x01F08: [0x0391, 0x0313],
    0x01F09: [0x0391, 0x0314],
    0x01F0A: [0x1F08, 0x0300],
    0x01F0B: [0x1F09, 0x0300],
    0x01F0C: [0x1F08, 0x0301],
    0x01F0D: [0x1F09, 0x0301],
    0x01F0E: [0x1F08, 0x0342],
    0x01F0F: [0x1F09, 0x0342],
    0x01F10: [0x03B5, 0x0313],
    0x01F11: [0x03B5, 0x0314],
    0x01F12: [0x1F10, 0x0300],
    0x01F13: [0x1F11, 0x0300],
    0x01F14: [0x1F10, 0x0301],
    0x01F15: [0x1F11, 0x0301],
    0x01F18: [0x0395, 0x0313],
    0x01F19: [0x0395, 0x0314],
    0x01F1A: [0x1F18, 0x0300],
    0x01F1B: [0x1F19, 0x0300],
    0x01F1C: [0x1F18, 0x0301],
    0x01F1D: [0x1F19, 0x0301],
    0x01F20: [0x03B7, 0x0313],
    0x01F21: [0x03B7, 0x0314],
    0x01F22: [0x1F20, 0x0300],
    0x01F23: [0x1F21, 0x0300],
    0x01F24: [0x1F20, 0x0301],
    0x01F25: [0x1F21, 0x0301],
    0x01F26: [0x1F20, 0x0342],
    0x01F27: [0x1F21, 0x0342],
    0x01F28: [0x0397, 0x0313],
    0x01F29: [0x0397, 0x0314],
    0x01F2A: [0x1F28, 0x0300],
    0x01F2B: [0x1F29, 0x0300],
    0x01F2C: [0x1F28, 0x0301],
    0x01F2D: [0x1F29, 0x0301],
    0x01F2E: [0x1F28, 0x0342],
    0x01F2F: [0x1F29, 0x0342],
    0x01F30: [0x03B9, 0x0313],
    0x01F31: [0x03B9, 0x0314],
    0x01F32: [0x1F30, 0x0300],
    0x01F33: [0x1F31, 0x0300],
    0x01F34: [0x1F30, 0x0301],
    0x01F35: [0x1F31, 0x0301],
    0x01F36: [0x1F30, 0x0342],
    0x01F37: [0x1F31, 0x0342],
    0x01F38: [0x0399, 0x0313],
    0x01F39: [0x0399, 0x0314],
    0x01F3A: [0x1F38, 0x0300],
    0x01F3B: [0x1F39, 0x0300],
    0x01F3C: [0x1F38, 0x0301],
    0x01F3D: [0x1F39, 0x0301],
    0x01F3E: [0x1F38, 0x0342],
    0x01F3F: [0x1F39, 0x0342],
    0x01F40: [0x03BF, 0x0313],
    0x01F41: [0x03BF, 0x0314],
    0x01F42: [0x1F40, 0x0300],
    0x01F43: [0x1F41, 0x0300],
    0x01F44: [0x1F40, 0x0301],
    0x01F45: [0x1F41, 0x0301],
    0x01F48: [0x039F, 0x0313],
    0x01F49: [0x039F, 0x0314],
    0x01F4A: [0x1F48, 0x0300],
    0x01F4B: [0x1F49, 0x0300],
    0x01F4C: [0x1F48, 0x0301],
    0x01F4D: [0x1F49, 0x0301],
    0x01F50: [0x03C5, 0x0313],
    0x01F51: [0x03C5, 0x0314],
    0x01F52: [0x1F50, 0x0300],
    0x01F53: [0x1F51, 0x0300],
    0x01F54: [0x1F50, 0x0301],
    0x01F55: [0x1F51, 0x0301],
    0x01F56: [0x1F50, 0x0342],
    0x01F57: [0x1F51, 0x0342],
    0x01F59: [0x03A5, 0x0314],
    0x01F5B: [0x1F59, 0x0300],
    0x01F5D: [0x1F59, 0x0301],
    0x01F5F: [0x1F59, 0x0342],
    0x01F60: [0x03C9, 0x0313],
    0x01F61: [0x03C9, 0x0314],
    0x01F62: [0x1F60, 0x0300],
    0x01F63: [0x1F61, 0x0300],
    0x01F64: [0x1F60, 0x0301],
    0x01F65: [0x1F61, 0x0301],
    0x01F66: [0x1F60, 0x0342],
    0x01F67: [0x1F61, 0x0342],
    0x01F68: [0x03A9, 0x0313],
    0x01F69: [0x03A9, 0x0314],
    0x01F6A: [0x1F68, 0x0300],
    0x01F6B: [0x1F69, 0x0300],
    0x01F6C: [0x1F68, 0x0301],
    0x01F6D: [0x1F69, 0x0301],
    0x01F6E: [0x1F68, 0x0342],
    0x01F6F: [0x1F69, 0x0342],
    0x01F70: [0x03B1, 0x0300],
    0x01F71: [0x03AC],
    0x01F72: [0x03B5, 0x0300],
    0x01F73: [0x03AD],
    0x01F74: [0x03B7, 0x0300],
    0x01F75: [0x03AE],
    0x01F76: [0x03B9, 0x0300],
    0x01F77: [0x03AF],
    0x01F78: [0x03BF, 0x0300],
    0x01F79: [0x03CC],
    0x01F7A: [0x03C5, 0x0300],
    0x01F7B: [0x03CD],
    0x01F7C: [0x03C9, 0x0300],
    0x01F7D: [0x03CE],
    0x01F80: [0x1F00, 0x0345],
    0x01F81: [0x1F01, 0x0345],
    0x01F82: [0x1F02, 0x0345],
    0x01F83: [0x1F03, 0x0345],
    0x01F84: [0x1F04, 0x0345],
    0x01F85: [0x1F05, 0x0345],
    0x01F86: [0x1F06, 0x0345],
    0x01F87: [0x1F07, 0x0345],
    0x01F88: [0x1F08, 0x0345],
    0x01F89: [0x1F09, 0x0345],
    0x01F8A: [0x1F0A, 0x0345],
    0x01F8B: [0x1F0B, 0x0345],
    0x01F8C: [0x1F0C, 0x0345],
    0x01F8D: [0x1F0D, 0x0345],
    0x01F8E: [0x1F0E, 0x0345],
    0x01F8F: [0x1F0F, 0x0345],
    0x01F90: [0x1F20, 0x0345],
    0x01F91: [0x1F21, 0x0345],
    0x01F92: [0x1F22, 0x0345],
    0x01F93: [0x1F23, 0x0345],
    0x01F94: [0x1F24, 0x0345],
    0x01F95: [0x1F25, 0x0345],
    0x01F96: [0x1F26, 0x0345],
    0x01F97: [0x1F27, 0x0345],
    0x01F98: [0x1F28, 0x0345],
    0x01F99: [0x1F29, 0x0345],
    0x01F9A: [0x1F2A, 0x0345],
    0x01F9B: [0x1F2B, 0x0345],
    0x01F9C: [0x1F2C, 0x0345],
    0x01F9D: [0x1F2D, 0x0345],
    0x01F9E: [0x1F2E, 0x0345],
    0x01F9F: [0x1F2F, 0x0345],
    0x01FA0: [0x1F60, 0x0345],
    0x01FA1: [0x1F61, 0x0345],
    0x01FA2: [0x1F62, 0x0345],
    0x01FA3: [0x1F63, 0x0345],
    0x01FA4: [0x1F64, 0x0345],
    0x01FA5: [0x1F65, 0x0345],
    0x01FA6: [0x1F66, 0x0345],
    0x01FA7: [0x1F67, 0x0345],
    0x01FA8: [0x1F68, 0x0345],
    0x01FA9: [0x1F69, 0x0345],
    0x01FAA: [0x1F6A, 0x0345],
    0x01FAB: [0x1F6B, 0x0345],
    0x01FAC: [0x1F6C, 0x0345],
    0x01FAD: [0x1F6D, 0x0345],
    0x01FAE: [0x1F6E, 0x0345],
    0x01FAF: [0x1F6F, 0x0345],
    0x01FB0: [0x03B1, 0x0306],
    0x01FB1: [0x03B1, 0x0304],
    0x01FB2: [0x1F70, 0x0345],
    0x01FB3: [0x03B1, 0x0345],
    0x01FB4: [0x03AC, 0x0345],
    0x01FB6: [0x03B1, 0x0342],
    0x01FB7: [0x1FB6, 0x0345],
    0x01FB8: [0x0391, 0x0306],
    0x01FB9: [0x0391, 0x0304],
    0x01FBA: [0x0391, 0x0300],
    0x01FBB: [0x0386],
    0x01FBC: [0x0391, 0x0345],
    0x01FBD: ["<compat>", 0x0020, 0x0313],
    0x01FBE: [0x03B9],
    0x01FBF: ["<compat>", 0x0020, 0x0313],
    0x01FC0: ["<compat>", 0x0020, 0x0342],
    0x01FC1: [0x00A8, 0x0342],
    0x01FC2: [0x1F74, 0x0345],
    0x01FC3: [0x03B7, 0x0345],
    0x01FC4: [0x03AE, 0x0345],
    0x01FC6: [0x03B7, 0x0342],
    0x01FC7: [0x1FC6, 0x0345],
    0x01FC8: [0x0395, 0x0300],
    0x01FC9: [0x0388],
    0x01FCA: [0x0397, 0x0300],
    0x01FCB: [0x0389],
    0x01FCC: [0x0397, 0x0345],
    0x01FCD: [0x1FBF, 0x0300],
    0x01FCE: [0x1FBF, 0x0301],
    0x01FCF: [0x1FBF, 0x0342],
    0x01FD0: [0x03B9, 0x0306],
    0x01FD1: [0x03B9, 0x0304],
    0x01FD2: [0x03CA, 0x0300],
    0x01FD3: [0x0390],
    0x01FD6: [0x03B9, 0x0342],
    0x01FD7: [0x03CA, 0x0342],
    0x01FD8: [0x0399, 0x0306],
    0x01FD9: [0x0399, 0x0304],
    0x01FDA: [0x0399, 0x0300],
    0x01FDB: [0x038A],
    0x01FDD: [0x1FFE, 0x0300],
    0x01FDE: [0x1FFE, 0x0301],
    0x01FDF: [0x1FFE, 0x0342],
    0x01FE0: [0x03C5, 0x0306],
    0x01FE1: [0x03C5, 0x0304],
    0x01FE2: [0x03CB, 0x0300],
    0x01FE3: [0x03B0],
    0x01FE4: [0x03C1, 0x0313],
    0x01FE5: [0x03C1, 0x0314],
    0x01FE6: [0x03C5, 0x0342],
    0x01FE7: [0x03CB, 0x0342],
    0x01FE8: [0x03A5, 0x0306],
    0x01FE9: [0x03A5, 0x0304],
    0x01FEA: [0x03A5, 0x0300],
    0x01FEB: [0x038E],
    0x01FEC: [0x03A1, 0x0314],
    0x01FED: [0x00A8, 0x0300],
    0x01FEE: [0x0385],
    0x01FEF: [0x0060],
    0x01FF2: [0x1F7C, 0x0345],
    0x01FF3: [0x03C9, 0x0345],
    0x01FF4: [0x03CE, 0x0345],
    0x01FF6: [0x03C9, 0x0342],
    0x01FF7: [0x1FF6, 0x0345],
    0x01FF8: [0x039F, 0x0300],
    0x01FF9: [0x038C],
    0x01FFA: [0x03A9, 0x0300],
    0x01FFB: [0x038F],
    0x01FFC: [0x03A9, 0x0345],
    0x01FFD: [0x00B4],
    0x01FFE: ["<compat>", 0x0020, 0x0314],
    0x02000: [0x2002],
    0x02001: [0x2003],
    0x02002: ["<compat>", 0x0020],
    0x02003: ["<compat>", 0x0020],
    0x02004: ["<compat>", 0x0020],
    0x02005: ["<compat>", 0x0020],
    0x02006: ["<compat>", 0x0020],
    0x02007: ["<noBreak>", 0x0020],
    0x02008: ["<compat>", 0x0020],
    0x02009: ["<compat>", 0x0020],
    0x0200A: ["<compat>", 0x0020],
    0x02011: ["<noBreak>", 0x2010],
    0x02017: ["<compat>", 0x0020, 0x0333],
    0x02024: ["<compat>", 0x002E],
    0x02025: ["<compat>", 0x002E, 0x002E],
    0x02026: ["<compat>", 0x002E, 0x002E, 0x002E],
    0x0202F: ["<noBreak>", 0x0020],
    0x02033: ["<compat>", 0x2032, 0x2032],
    0x02034: ["<compat>", 0x2032, 0x2032, 0x2032],
    0x02036: ["<compat>", 0x2035, 0x2035],
    0x02037: ["<compat>", 0x2035, 0x2035, 0x2035],
    0x0203C: ["<compat>", 0x0021, 0x0021],
    0x0203E: ["<compat>", 0x0020, 0x0305],
    0x02047: ["<compat>", 0x003F, 0x003F],
    0x02048: ["<compat>", 0x003F, 0x0021],
    0x02049: ["<compat>", 0x0021, 0x003F],
    0x02057: ["<compat>", 0x2032, 0x2032, 0x2032, 0x2032],
    0x0205F: ["<compat>", 0x0020],
    0x02070: ["<super>", 0x0030],
    0x02071: ["<super>", 0x0069],
    0x02074: ["<super>", 0x0034],
    0x02075: ["<super>", 0x0035],
    0x02076: ["<super>", 0x0036],
    0x02077: ["<super>", 0x0037],
    0x02078: ["<super>", 0x0038],
    0x02079: ["<super>", 0x0039],
    0x0207A: ["<super>", 0x002B],
    0x0207B: ["<super>", 0x2212],
    0x0207C: ["<super>", 0x003D],
    0x0207D: ["<super>", 0x0028],
    0x0207E: ["<super>", 0x0029],
    0x0207F: ["<super>", 0x006E],
    0x02080: ["<sub>", 0x0030],
    0x02081: ["<sub>", 0x0031],
    0x02082: ["<sub>", 0x0032],
    0x02083: ["<sub>", 0x0033],
    0x02084: ["<sub>", 0x0034],
    0x02085: ["<sub>", 0x0035],
    0x02086: ["<sub>", 0x0036],
    0x02087: ["<sub>", 0x0037],
    0x02088: ["<sub>", 0x0038],
    0x02089: ["<sub>", 0x0039],
    0x0208A: ["<sub>", 0x002B],
    0x0208B: ["<sub>", 0x2212],
    0x0208C: ["<sub>", 0x003D],
    0x0208D: ["<sub>", 0x0028],
    0x0208E: ["<sub>", 0x0029],
    0x02090: ["<sub>", 0x0061],
    0x02091: ["<sub>", 0x0065],
    0x02092: ["<sub>", 0x006F],
    0x02093: ["<sub>", 0x0078],
    0x02094: ["<sub>", 0x0259],
    0x02095: ["<sub>", 0x0068],
    0x02096: ["<sub>", 0x006B],
    0x02097: ["<sub>", 0x006C],
    0x02098: ["<sub>", 0x006D],
    0x02099: ["<sub>", 0x006E],
    0x0209A: ["<sub>", 0x0070],
    0x0209B: ["<sub>", 0x0073],
    0x0209C: ["<sub>", 0x0074],
    0x020A8: ["<compat>", 0x0052, 0x0073],
    0x02100: ["<compat>", 0x0061, 0x002F, 0x0063],
    0x02101: ["<compat>", 0x0061, 0x002F, 0x0073],
    0x02102: ["<font>", 0x0043],
    0x02103: ["<compat>", 0x00B0, 0x0043],
    0x02105: ["<compat>", 0x0063, 0x002F, 0x006F],
    0x02106: ["<compat>", 0x0063, 0x002F, 0x0075],
    0x02107: ["<compat>", 0x0190],
    0x02109: ["<compat>", 0x00B0, 0x0046],
    0x0210A: ["<font>", 0x0067],
    0x0210B: ["<font>", 0x0048],
    0x0210C: ["<font>", 0x0048],
    0x0210D: ["<font>", 0x0048],
    0x0210E: ["<font>", 0x0068],
    0x0210F: ["<font>", 0x0127],
    0x02110: ["<font>", 0x0049],
    0x02111: ["<font>", 0x0049],
    0x02112: ["<font>", 0x004C],
    0x02113: ["<font>", 0x006C],
    0x02115: ["<font>", 0x004E],
    0x02116: ["<compat>", 0x004E, 0x006F],
    0x02119: ["<font>", 0x0050],
    0x0211A: ["<font>", 0x0051],
    0x0211B: ["<font>", 0x0052],
    0x0211C: ["<font>", 0x0052],
    0x0211D: ["<font>", 0x0052],
    0x02120: ["<super>", 0x0053, 0x004D],
    0x02121: ["<compat>", 0x0054, 0x0045, 0x004C],
    0x02122: ["<super>", 0x0054, 0x004D],
    0x02124: ["<font>", 0x005A],
    0x02126: [0x03A9],
    0x02128: ["<font>", 0x005A],
    0x0212A: [0x004B],
    0x0212B: [0x00C5],
    0x0212C: ["<font>", 0x0042],
    0x0212D: ["<font>", 0x0043],
    0x0212F: ["<font>", 0x0065],
    0x02130: ["<font>", 0x0045],
    0x02131: ["<font>", 0x0046],
    0x02133: ["<font>", 0x004D],
    0x02134: ["<font>", 0x006F],
    0x02135: ["<compat>", 0x05D0],
    0x02136: ["<compat>", 0x05D1],
    0x02137: ["<compat>", 0x05D2],
    0x02138: ["<compat>", 0x05D3],
    0x02139: ["<font>", 0x0069],
    0x0213B: ["<compat>", 0x0046, 0x0041, 0x0058],
    0x0213C: ["<font>", 0x03C0],
    0x0213D: ["<font>", 0x03B3],
    0x0213E: ["<font>", 0x0393],
    0x0213F: ["<font>", 0x03A0],
    0x02140: ["<font>", 0x2211],
    0x02145: ["<font>", 0x0044],
    0x02146: ["<font>", 0x0064],
    0x02147: ["<font>", 0x0065],
    0x02148: ["<font>", 0x0069],
    0x02149: ["<font>", 0x006A],
    0x02150: ["<fraction>", 0x0031, 0x2044, 0x0037],
    0x02151: ["<fraction>", 0x0031, 0x2044, 0x0039],
    0x02152: ["<fraction>", 0x0031, 0x2044, 0x0031, 0x0030],
    0x02153: ["<fraction>", 0x0031, 0x2044, 0x0033],
    0x02154: ["<fraction>", 0x0032, 0x2044, 0x0033],
    0x02155: ["<fraction>", 0x0031, 0x2044, 0x0035],
    0x02156: ["<fraction>", 0x0032, 0x2044, 0x0035],
    0x02157: ["<fraction>", 0x0033, 0x2044, 0x0035],
    0x02158: ["<fraction>", 0x0034, 0x2044, 0x0035],
    0x02159: ["<fraction>", 0x0031, 0x2044, 0x0036],
    0x0215A: ["<fraction>", 0x0035, 0x2044, 0x0036],
    0x0215B: ["<fraction>", 0x0031, 0x2044, 0x0038],
    0x0215C: ["<fraction>", 0x0033, 0x2044, 0x0038],
    0x0215D: ["<fraction>", 0x0035, 0x2044, 0x0038],
    0x0215E: ["<fraction>", 0x0037, 0x2044, 0x0038],
    0x0215F: ["<fraction>", 0x0031, 0x2044],
    0x02160: ["<compat>", 0x0049],
    0x02161: ["<compat>", 0x0049, 0x0049],
    0x02162: ["<compat>", 0x0049, 0x0049, 0x0049],
    0x02163: ["<compat>", 0x0049, 0x0056],
    0x02164: ["<compat>", 0x0056],
    0x02165: ["<compat>", 0x0056, 0x0049],
    0x02166: ["<compat>", 0x0056, 0x0049, 0x0049],
    0x02167: ["<compat>", 0x0056, 0x0049, 0x0049, 0x0049],
    0x02168: ["<compat>", 0x0049, 0x0058],
    0x02169: ["<compat>", 0x0058],
    0x0216A: ["<compat>", 0x0058, 0x0049],
    0x0216B: ["<compat>", 0x0058, 0x0049, 0x0049],
    0x0216C: ["<compat>", 0x004C],
    0x0216D: ["<compat>", 0x0043],
    0x0216E: ["<compat>", 0x0044],
    0x0216F: ["<compat>", 0x004D],
    0x02170: ["<compat>", 0x0069],
    0x02171: ["<compat>", 0x0069, 0x0069],
    0x02172: ["<compat>", 0x0069, 0x0069, 0x0069],
    0x02173: ["<compat>", 0x0069, 0x0076],
    0x02174: ["<compat>", 0x0076],
    0x02175: ["<compat>", 0x0076, 0x0069],
    0x02176: ["<compat>", 0x0076, 0x0069, 0x0069],
    0x02177: ["<compat>", 0x0076, 0x0069, 0x0069, 0x0069],
    0x02178: ["<compat>", 0x0069, 0x0078],
    0x02179: ["<compat>", 0x0078],
    0x0217A: ["<compat>", 0x0078, 0x0069],
    0x0217B: ["<compat>", 0x0078, 0x0069, 0x0069],
    0x0217C: ["<compat>", 0x006C],
    0x0217D: ["<compat>", 0x0063],
    0x0217E: ["<compat>", 0x0064],
    0x0217F: ["<compat>", 0x006D],
    0x02189: ["<fraction>", 0x0030, 0x2044, 0x0033],
    0x0219A: [0x2190, 0x0338],
    0x0219B: [0x2192, 0x0338],
    0x021AE: [0x2194, 0x0338],
    0x021CD: [0x21D0, 0x0338],
    0x021CE: [0x21D4, 0x0338],
    0x021CF: [0x21D2, 0x0338],
    0x02204: [0x2203, 0x0338],
    0x02209: [0x2208, 0x0338],
    0x0220C: [0x220B, 0x0338],
    0x02224: [0x2223, 0x0338],
    0x02226: [0x2225, 0x0338],
    0x0222C: ["<compat>", 0x222B, 0x222B],
    0x0222D: ["<compat>", 0x222B, 0x222B, 0x222B],
    0x0222F: ["<compat>", 0x222E, 0x222E],
    0x02230: ["<compat>", 0x222E, 0x222E, 0x222E],
    0x02241: [0x223C, 0x0338],
    0x02244: [0x2243, 0x0338],
    0x02247: [0x2245, 0x0338],
    0x02249: [0x2248, 0x0338],
    0x02260: [0x003D, 0x0338],
    0x02262: [0x2261, 0x0338],
    0x0226D: [0x224D, 0x0338],
    0x0226E: [0x003C, 0x0338],
    0x0226F: [0x003E, 0x0338],
    0x02270: [0x2264, 0x0338],
    0x02271: [0x2265, 0x0338],
    0x02274: [0x2272, 0x0338],
    0x02275: [0x2273, 0x0338],
    0x02278: [0x2276, 0x0338],
    0x02279: [0x2277, 0x0338],
    0x02280: [0x227A, 0x0338],
    0x02281: [0x227B, 0x0338],
    0x02284: [0x2282, 0x0338],
    0x02285: [0x2283, 0x0338],
    0x02288: [0x2286, 0x0338],
    0x02289: [0x2287, 0x0338],
    0x022AC: [0x22A2, 0x0338],
    0x022AD: [0x22A8, 0x0338],
    0x022AE: [0x22A9, 0x0338],
    0x022AF: [0x22AB, 0x0338],
    0x022E0: [0x227C, 0x0338],
    0x022E1: [0x227D, 0x0338],
    0x022E2: [0x2291, 0x0338],
    0x022E3: [0x2292, 0x0338],
    0x022EA: [0x22B2, 0x0338],
    0x022EB: [0x22B3, 0x0338],
    0x022EC: [0x22B4, 0x0338],
    0x022ED: [0x22B5, 0x0338],
    0x02329: [0x3008],
    0x0232A: [0x3009],
    0x02460: ["<circle>", 0x0031],
    0x02461: ["<circle>", 0x0032],
    0x02462: ["<circle>", 0x0033],
    0x02463: ["<circle>", 0x0034],
    0x02464: ["<circle>", 0x0035],
    0x02465: ["<circle>", 0x0036],
    0x02466: ["<circle>", 0x0037],
    0x02467: ["<circle>", 0x0038],
    0x02468: ["<circle>", 0x0039],
    0x02469: ["<circle>", 0x0031, 0x0030],
    0x0246A: ["<circle>", 0x0031, 0x0031],
    0x0246B: ["<circle>", 0x0031, 0x0032],
    0x0246C: ["<circle>", 0x0031, 0x0033],
    0x0246D: ["<circle>", 0x0031, 0x0034],
    0x0246E: ["<circle>", 0x0031, 0x0035],
    0x0246F: ["<circle>", 0x0031, 0x0036],
    0x02470: ["<circle>", 0x0031, 0x0037],
    0x02471: ["<circle>", 0x0031, 0x0038],
    0x02472: ["<circle>", 0x0031, 0x0039],
    0x02473: ["<circle>", 0x0032, 0x0030],
    0x02474: ["<compat>", 0x0028, 0x0031, 0x0029],
    0x02475: ["<compat>", 0x0028, 0x0032, 0x0029],
    0x02476: ["<compat>", 0x0028, 0x0033, 0x0029],
    0x02477: ["<compat>", 0x0028, 0x0034, 0x0029],
    0x02478: ["<compat>", 0x0028, 0x0035, 0x0029],
    0x02479: ["<compat>", 0x0028, 0x0036, 0x0029],
    0x0247A: ["<compat>", 0x0028, 0x0037, 0x0029],
    0x0247B: ["<compat>", 0x0028, 0x0038, 0x0029],
    0x0247C: ["<compat>", 0x0028, 0x0039, 0x0029],
    0x0247D: ["<compat>", 0x0028, 0x0031, 0x0030, 0x0029],
    0x0247E: ["<compat>", 0x0028, 0x0031, 0x0031, 0x0029],
    0x0247F: ["<compat>", 0x0028, 0x0031, 0x0032, 0x0029],
    0x02480: ["<compat>", 0x0028, 0x0031, 0x0033, 0x0029],
    0x02481: ["<compat>", 0x0028, 0x0031, 0x0034, 0x0029],
    0x02482: ["<compat>", 0x0028, 0x0031, 0x0035, 0x0029],
    0x02483: ["<compat>", 0x0028, 0x0031, 0x0036, 0x0029],
    0x02484: ["<compat>", 0x0028, 0x0031, 0x0037, 0x0029],
    0x02485: ["<compat>", 0x0028, 0x0031, 0x0038, 0x0029],
    0x02486: ["<compat>", 0x0028, 0x0031, 0x0039, 0x0029],
    0x02487: ["<compat>", 0x0028, 0x0032, 0x0030, 0x0029],
    0x02488: ["<compat>", 0x0031, 0x002E],
    0x02489: ["<compat>", 0x0032, 0x002E],
    0x0248A: ["<compat>", 0x0033, 0x002E],
    0x0248B: ["<compat>", 0x0034, 0x002E],
    0x0248C: ["<compat>", 0x0035, 0x002E],
    0x0248D: ["<compat>", 0x0036, 0x002E],
    0x0248E: ["<compat>", 0x0037, 0x002E],
    0x0248F: ["<compat>", 0x0038, 0x002E],
    0x02490: ["<compat>", 0x0039, 0x002E],
    0x02491: ["<compat>", 0x0031, 0x0030, 0x002E],
    0x02492: ["<compat>", 0x0031, 0x0031, 0x002E],
    0x02493: ["<compat>", 0x0031, 0x0032, 0x002E],
    0x02494: ["<compat>", 0x0031, 0x0033, 0x002E],
    0x02495: ["<compat>", 0x0031, 0x0034, 0x002E],
    0x02496: ["<compat>", 0x0031, 0x0035, 0x002E],
    0x02497: ["<compat>", 0x0031, 0x0036, 0x002E],
    0x02498: ["<compat>", 0x0031, 0x0037, 0x002E],
    0x02499: ["<compat>", 0x0031, 0x0038, 0x002E],
    0x0249A: ["<compat>", 0x0031, 0x0039, 0x002E],
    0x0249B: ["<compat>", 0x0032, 0x0030, 0x002E],
    0x0249C: ["<compat>", 0x0028, 0x0061, 0x0029],
    0x0249D: ["<compat>", 0x0028, 0x0062, 0x0029],
    0x0249E: ["<compat>", 0x0028, 0x0063, 0x0029],
    0x0249F: ["<compat>", 0x0028, 0x0064, 0x0029],
    0x024A0: ["<compat>", 0x0028, 0x0065, 0x0029],
    0x024A1: ["<compat>", 0x0028, 0x0066, 0x0029],
    0x024A2: ["<compat>", 0x0028, 0x0067, 0x0029],
    0x024A3: ["<compat>", 0x0028, 0x0068, 0x0029],
    0x024A4: ["<compat>", 0x0028, 0x0069, 0x0029],
    0x024A5: ["<compat>", 0x0028, 0x006A, 0x0029],
    0x024A6: ["<compat>", 0x0028, 0x006B, 0x0029],
    0x024A7: ["<compat>", 0x0028, 0x006C, 0x0029],
    0x024A8: ["<compat>", 0x0028, 0x006D, 0x0029],
    0x024A9: ["<compat>", 0x0028, 0x006E, 0x0029],
    0x024AA: ["<compat>", 0x0028, 0x006F, 0x0029],
    0x024AB: ["<compat>", 0x0028, 0x0070, 0x0029],
    0x024AC: ["<compat>", 0x0028, 0x0071, 0x0029],
    0x024AD: ["<compat>", 0x0028, 0x0072, 0x0029],
    0x024AE: ["<compat>", 0x0028, 0x0073, 0x0029],
    0x024AF: ["<compat>", 0x0028, 0x0074, 0x0029],
    0x024B0: ["<compat>", 0x0028, 0x0075, 0x0029],
    0x024B1: ["<compat>", 0x0028, 0x0076, 0x0029],
    0x024B2: ["<compat>", 0x0028, 0x0077, 0x0029],
    0x024B3: ["<compat>", 0x0028, 0x0078, 0x0029],
    0x024B4: ["<compat>", 0x0028, 0x0079, 0x0029],
    0x024B5: ["<compat>", 0x0028, 0x007A, 0x0029],
    0x024B6: ["<circle>", 0x0041],
    0x024B7: ["<circle>", 0x0042],
    0x024B8: ["<circle>", 0x0043],
    0x024B9: ["<circle>", 0x0044],
    0x024BA: ["<circle>", 0x0045],
    0x024BB: ["<circle>", 0x0046],
    0x024BC: ["<circle>", 0x0047],
    0x024BD: ["<circle>", 0x0048],
    0x024BE: ["<circle>", 0x0049],
    0x024BF: ["<circle>", 0x004A],
    0x024C0: ["<circle>", 0x004B],
    0x024C1: ["<circle>", 0x004C],
    0x024C2: ["<circle>", 0x004D],
    0x024C3: ["<circle>", 0x004E],
    0x024C4: ["<circle>", 0x004F],
    0x024C5: ["<circle>", 0x0050],
    0x024C6: ["<circle>", 0x0051],
    0x024C7: ["<circle>", 0x0052],
    0x024C8: ["<circle>", 0x0053],
    0x024C9: ["<circle>", 0x0054],
    0x024CA: ["<circle>", 0x0055],
    0x024CB: ["<circle>", 0x0056],
    0x024CC: ["<circle>", 0x0057],
    0x024CD: ["<circle>", 0x0058],
    0x024CE: ["<circle>", 0x0059],
    0x024CF: ["<circle>", 0x005A],
    0x024D0: ["<circle>", 0x0061],
    0x024D1: ["<circle>", 0x0062],
    0x024D2: ["<circle>", 0x0063],
    0x024D3: ["<circle>", 0x0064],
    0x024D4: ["<circle>", 0x0065],
    0x024D5: ["<circle>", 0x0066],
    0x024D6: ["<circle>", 0x0067],
    0x024D7: ["<circle>", 0x0068],
    0x024D8: ["<circle>", 0x0069],
    0x024D9: ["<circle>", 0x006A],
    0x024DA: ["<circle>", 0x006B],
    0x024DB: ["<circle>", 0x006C],
    0x024DC: ["<circle>", 0x006D],
    0x024DD: ["<circle>", 0x006E],
    0x024DE: ["<circle>", 0x006F],
    0x024DF: ["<circle>", 0x0070],
    0x024E0: ["<circle>", 0x0071],
    0x024E1: ["<circle>", 0x0072],
    0x024E2: ["<circle>", 0x0073],
    0x024E3: ["<circle>", 0x0074],
    0x024E4: ["<circle>", 0x0075],
    0x024E5: ["<circle>", 0x0076],
    0x024E6: ["<circle>", 0x0077],
    0x024E7: ["<circle>", 0x0078],
    0x024E8: ["<circle>", 0x0079],
    0x024E9: ["<circle>", 0x007A],
    0x024EA: ["<circle>", 0x0030],
    0x02A0C: ["<compat>", 0x222B, 0x222B, 0x222B, 0x222B],
    0x02A74: ["<compat>", 0x003A, 0x003A, 0x003D],
    0x02A75: ["<compat>", 0x003D, 0x003D],
    0x02A76: ["<compat>", 0x003D, 0x003D, 0x003D],
    0x02ADC: [0x2ADD, 0x0338],
    0x02C7C: ["<sub>", 0x006A],
    0x02C7D: ["<super>", 0x0056],
    0x02D6F: ["<super>", 0x2D61],
    0x02E9F: ["<compat>", 0x6BCD],
    0x02EF3: ["<compat>", 0x9F9F],
    0x02F00: ["<compat>", 0x4E00],
    0x02F01: ["<compat>", 0x4E28],
    0x02F02: ["<compat>", 0x4E36],
    0x02F03: ["<compat>", 0x4E3F],
    0x02F04: ["<compat>", 0x4E59],
    0x02F05: ["<compat>", 0x4E85],
    0x02F06: ["<compat>", 0x4E8C],
    0x02F07: ["<compat>", 0x4EA0],
    0x02F08: ["<compat>", 0x4EBA],
    0x02F09: ["<compat>", 0x513F],
    0x02F0A: ["<compat>", 0x5165],
    0x02F0B: ["<compat>", 0x516B],
    0x02F0C: ["<compat>", 0x5182],
    0x02F0D: ["<compat>", 0x5196],
    0x02F0E: ["<compat>", 0x51AB],
    0x02F0F: ["<compat>", 0x51E0],
    0x02F10: ["<compat>", 0x51F5],
    0x02F11: ["<compat>", 0x5200],
    0x02F12: ["<compat>", 0x529B],
    0x02F13: ["<compat>", 0x52F9],
    0x02F14: ["<compat>", 0x5315],
    0x02F15: ["<compat>", 0x531A],
    0x02F16: ["<compat>", 0x5338],
    0x02F17: ["<compat>", 0x5341],
    0x02F18: ["<compat>", 0x535C],
    0x02F19: ["<compat>", 0x5369],
    0x02F1A: ["<compat>", 0x5382],
    0x02F1B: ["<compat>", 0x53B6],
    0x02F1C: ["<compat>", 0x53C8],
    0x02F1D: ["<compat>", 0x53E3],
    0x02F1E: ["<compat>", 0x56D7],
    0x02F1F: ["<compat>", 0x571F],
    0x02F20: ["<compat>", 0x58EB],
    0x02F21: ["<compat>", 0x5902],
    0x02F22: ["<compat>", 0x590A],
    0x02F23: ["<compat>", 0x5915],
    0x02F24: ["<compat>", 0x5927],
    0x02F25: ["<compat>", 0x5973],
    0x02F26: ["<compat>", 0x5B50],
    0x02F27: ["<compat>", 0x5B80],
    0x02F28: ["<compat>", 0x5BF8],
    0x02F29: ["<compat>", 0x5C0F],
    0x02F2A: ["<compat>", 0x5C22],
    0x02F2B: ["<compat>", 0x5C38],
    0x02F2C: ["<compat>", 0x5C6E],
    0x02F2D: ["<compat>", 0x5C71],
    0x02F2E: ["<compat>", 0x5DDB],
    0x02F2F: ["<compat>", 0x5DE5],
    0x02F30: ["<compat>", 0x5DF1],
    0x02F31: ["<compat>", 0x5DFE],
    0x02F32: ["<compat>", 0x5E72],
    0x02F33: ["<compat>", 0x5E7A],
    0x02F34: ["<compat>", 0x5E7F],
    0x02F35: ["<compat>", 0x5EF4],
    0x02F36: ["<compat>", 0x5EFE],
    0x02F37: ["<compat>", 0x5F0B],
    0x02F38: ["<compat>", 0x5F13],
    0x02F39: ["<compat>", 0x5F50],
    0x02F3A: ["<compat>", 0x5F61],
    0x02F3B: ["<compat>", 0x5F73],
    0x02F3C: ["<compat>", 0x5FC3],
    0x02F3D: ["<compat>", 0x6208],
    0x02F3E: ["<compat>", 0x6236],
    0x02F3F: ["<compat>", 0x624B],
    0x02F40: ["<compat>", 0x652F],
    0x02F41: ["<compat>", 0x6534],
    0x02F42: ["<compat>", 0x6587],
    0x02F43: ["<compat>", 0x6597],
    0x02F44: ["<compat>", 0x65A4],
    0x02F45: ["<compat>", 0x65B9],
    0x02F46: ["<compat>", 0x65E0],
    0x02F47: ["<compat>", 0x65E5],
    0x02F48: ["<compat>", 0x66F0],
    0x02F49: ["<compat>", 0x6708],
    0x02F4A: ["<compat>", 0x6728],
    0x02F4B: ["<compat>", 0x6B20],
    0x02F4C: ["<compat>", 0x6B62],
    0x02F4D: ["<compat>", 0x6B79],
    0x02F4E: ["<compat>", 0x6BB3],
    0x02F4F: ["<compat>", 0x6BCB],
    0x02F50: ["<compat>", 0x6BD4],
    0x02F51: ["<compat>", 0x6BDB],
    0x02F52: ["<compat>", 0x6C0F],
    0x02F53: ["<compat>", 0x6C14],
    0x02F54: ["<compat>", 0x6C34],
    0x02F55: ["<compat>", 0x706B],
    0x02F56: ["<compat>", 0x722A],
    0x02F57: ["<compat>", 0x7236],
    0x02F58: ["<compat>", 0x723B],
    0x02F59: ["<compat>", 0x723F],
    0x02F5A: ["<compat>", 0x7247],
    0x02F5B: ["<compat>", 0x7259],
    0x02F5C: ["<compat>", 0x725B],
    0x02F5D: ["<compat>", 0x72AC],
    0x02F5E: ["<compat>", 0x7384],
    0x02F5F: ["<compat>", 0x7389],
    0x02F60: ["<compat>", 0x74DC],
    0x02F61: ["<compat>", 0x74E6],
    0x02F62: ["<compat>", 0x7518],
    0x02F63: ["<compat>", 0x751F],
    0x02F64: ["<compat>", 0x7528],
    0x02F65: ["<compat>", 0x7530],
    0x02F66: ["<compat>", 0x758B],
    0x02F67: ["<compat>", 0x7592],
    0x02F68: ["<compat>", 0x7676],
    0x02F69: ["<compat>", 0x767D],
    0x02F6A: ["<compat>", 0x76AE],
    0x02F6B: ["<compat>", 0x76BF],
    0x02F6C: ["<compat>", 0x76EE],
    0x02F6D: ["<compat>", 0x77DB],
    0x02F6E: ["<compat>", 0x77E2],
    0x02F6F: ["<compat>", 0x77F3],
    0x02F70: ["<compat>", 0x793A],
    0x02F71: ["<compat>", 0x79B8],
    0x02F72: ["<compat>", 0x79BE],
    0x02F73: ["<compat>", 0x7A74],
    0x02F74: ["<compat>", 0x7ACB],
    0x02F75: ["<compat>", 0x7AF9],
    0x02F76: ["<compat>", 0x7C73],
    0x02F77: ["<compat>", 0x7CF8],
    0x02F78: ["<compat>", 0x7F36],
    0x02F79: ["<compat>", 0x7F51],
    0x02F7A: ["<compat>", 0x7F8A],
    0x02F7B: ["<compat>", 0x7FBD],
    0x02F7C: ["<compat>", 0x8001],
    0x02F7D: ["<compat>", 0x800C],
    0x02F7E: ["<compat>", 0x8012],
    0x02F7F: ["<compat>", 0x8033],
    0x02F80: ["<compat>", 0x807F],
    0x02F81: ["<compat>", 0x8089],
    0x02F82: ["<compat>", 0x81E3],
    0x02F83: ["<compat>", 0x81EA],
    0x02F84: ["<compat>", 0x81F3],
    0x02F85: ["<compat>", 0x81FC],
    0x02F86: ["<compat>", 0x820C],
    0x02F87: ["<compat>", 0x821B],
    0x02F88: ["<compat>", 0x821F],
    0x02F89: ["<compat>", 0x826E],
    0x02F8A: ["<compat>", 0x8272],
    0x02F8B: ["<compat>", 0x8278],
    0x02F8C: ["<compat>", 0x864D],
    0x02F8D: ["<compat>", 0x866B],
    0x02F8E: ["<compat>", 0x8840],
    0x02F8F: ["<compat>", 0x884C],
    0x02F90: ["<compat>", 0x8863],
    0x02F91: ["<compat>", 0x897E],
    0x02F92: ["<compat>", 0x898B],
    0x02F93: ["<compat>", 0x89D2],
    0x02F94: ["<compat>", 0x8A00],
    0x02F95: ["<compat>", 0x8C37],
    0x02F96: ["<compat>", 0x8C46],
    0x02F97: ["<compat>", 0x8C55],
    0x02F98: ["<compat>", 0x8C78],
    0x02F99: ["<compat>", 0x8C9D],
    0x02F9A: ["<compat>", 0x8D64],
    0x02F9B: ["<compat>", 0x8D70],
    0x02F9C: ["<compat>", 0x8DB3],
    0x02F9D: ["<compat>", 0x8EAB],
    0x02F9E: ["<compat>", 0x8ECA],
    0x02F9F: ["<compat>", 0x8F9B],
    0x02FA0: ["<compat>", 0x8FB0],
    0x02FA1: ["<compat>", 0x8FB5],
    0x02FA2: ["<compat>", 0x9091],
    0x02FA3: ["<compat>", 0x9149],
    0x02FA4: ["<compat>", 0x91C6],
    0x02FA5: ["<compat>", 0x91CC],
    0x02FA6: ["<compat>", 0x91D1],
    0x02FA7: ["<compat>", 0x9577],
    0x02FA8: ["<compat>", 0x9580],
    0x02FA9: ["<compat>", 0x961C],
    0x02FAA: ["<compat>", 0x96B6],
    0x02FAB: ["<compat>", 0x96B9],
    0x02FAC: ["<compat>", 0x96E8],
    0x02FAD: ["<compat>", 0x9751],
    0x02FAE: ["<compat>", 0x975E],
    0x02FAF: ["<compat>", 0x9762],
    0x02FB0: ["<compat>", 0x9769],
    0x02FB1: ["<compat>", 0x97CB],
    0x02FB2: ["<compat>", 0x97ED],
    0x02FB3: ["<compat>", 0x97F3],
    0x02FB4: ["<compat>", 0x9801],
    0x02FB5: ["<compat>", 0x98A8],
    0x02FB6: ["<compat>", 0x98DB],
    0x02FB7: ["<compat>", 0x98DF],
    0x02FB8: ["<compat>", 0x9996],
    0x02FB9: ["<compat>", 0x9999],
    0x02FBA: ["<compat>", 0x99AC],
    0x02FBB: ["<compat>", 0x9AA8],
    0x02FBC: ["<compat>", 0x9AD8],
    0x02FBD: ["<compat>", 0x9ADF],
    0x02FBE: ["<compat>", 0x9B25],
    0x02FBF: ["<compat>", 0x9B2F],
    0x02FC0: ["<compat>", 0x9B32],
    0x02FC1: ["<compat>", 0x9B3C],
    0x02FC2: ["<compat>", 0x9B5A],
    0x02FC3: ["<compat>", 0x9CE5],
    0x02FC4: ["<compat>", 0x9E75],
    0x02FC5: ["<compat>", 0x9E7F],
    0x02FC6: ["<compat>", 0x9EA5],
    0x02FC7: ["<compat>", 0x9EBB],
    0x02FC8: ["<compat>", 0x9EC3],
    0x02FC9: ["<compat>", 0x9ECD],
    0x02FCA: ["<compat>", 0x9ED1],
    0x02FCB: ["<compat>", 0x9EF9],
    0x02FCC: ["<compat>", 0x9EFD],
    0x02FCD: ["<compat>", 0x9F0E],
    0x02FCE: ["<compat>", 0x9F13],
    0x02FCF: ["<compat>", 0x9F20],
    0x02FD0: ["<compat>", 0x9F3B],
    0x02FD1: ["<compat>", 0x9F4A],
    0x02FD2: ["<compat>", 0x9F52],
    0x02FD3: ["<compat>", 0x9F8D],
    0x02FD4: ["<compat>", 0x9F9C],
    0x02FD5: ["<compat>", 0x9FA0],
    0x03000: ["<wide>", 0x0020],
    0x03036: ["<compat>", 0x3012],
    0x03038: ["<compat>", 0x5341],
    0x03039: ["<compat>", 0x5344],
    0x0303A: ["<compat>", 0x5345],
    0x0304C: [0x304B, 0x3099],
    0x0304E: [0x304D, 0x3099],
    0x03050: [0x304F, 0x3099],
    0x03052: [0x3051, 0x3099],
    0x03054: [0x3053, 0x3099],
    0x03056: [0x3055, 0x3099],
    0x03058: [0x3057, 0x3099],
    0x0305A: [0x3059, 0x3099],
    0x0305C: [0x305B, 0x3099],
    0x0305E: [0x305D, 0x3099],
    0x03060: [0x305F, 0x3099],
    0x03062: [0x3061, 0x3099],
    0x03065: [0x3064, 0x3099],
    0x03067: [0x3066, 0x3099],
    0x03069: [0x3068, 0x3099],
    0x03070: [0x306F, 0x3099],
    0x03071: [0x306F, 0x309A],
    0x03073: [0x3072, 0x3099],
    0x03074: [0x3072, 0x309A],
    0x03076: [0x3075, 0x3099],
    0x03077: [0x3075, 0x309A],
    0x03079: [0x3078, 0x3099],
    0x0307A: [0x3078, 0x309A],
    0x0307C: [0x307B, 0x3099],
    0x0307D: [0x307B, 0x309A],
    0x03094: [0x3046, 0x3099],
    0x0309B: ["<compat>", 0x0020, 0x3099],
    0x0309C: ["<compat>", 0x0020, 0x309A],
    0x0309E: [0x309D, 0x3099],
    0x0309F: ["<vertical>", 0x3088, 0x308A],
    0x030AC: [0x30AB, 0x3099],
    0x030AE: [0x30AD, 0x3099],
    0x030B0: [0x30AF, 0x3099],
    0x030B2: [0x30B1, 0x3099],
    0x030B4: [0x30B3, 0x3099],
    0x030B6: [0x30B5, 0x3099],
    0x030B8: [0x30B7, 0x3099],
    0x030BA: [0x30B9, 0x3099],
    0x030BC: [0x30BB, 0x3099],
    0x030BE: [0x30BD, 0x3099],
    0x030C0: [0x30BF, 0x3099],
    0x030C2: [0x30C1, 0x3099],
    0x030C5: [0x30C4, 0x3099],
    0x030C7: [0x30C6, 0x3099],
    0x030C9: [0x30C8, 0x3099],
    0x030D0: [0x30CF, 0x3099],
    0x030D1: [0x30CF, 0x309A],
    0x030D3: [0x30D2, 0x3099],
    0x030D4: [0x30D2, 0x309A],
    0x030D6: [0x30D5, 0x3099],
    0x030D7: [0x30D5, 0x309A],
    0x030D9: [0x30D8, 0x3099],
    0x030DA: [0x30D8, 0x309A],
    0x030DC: [0x30DB, 0x3099],
    0x030DD: [0x30DB, 0x309A],
    0x030F4: [0x30A6, 0x3099],
    0x030F7: [0x30EF, 0x3099],
    0x030F8: [0x30F0, 0x3099],
    0x030F9: [0x30F1, 0x3099],
    0x030FA: [0x30F2, 0x3099],
    0x030FE: [0x30FD, 0x3099],
    0x030FF: ["<vertical>", 0x30B3, 0x30C8],
    0x03131: ["<compat>", 0x1100],
    0x03132: ["<compat>", 0x1101],
    0x03133: ["<compat>", 0x11AA],
    0x03134: ["<compat>", 0x1102],
    0x03135: ["<compat>", 0x11AC],
    0x03136: ["<compat>", 0x11AD],
    0x03137: ["<compat>", 0x1103],
    0x03138: ["<compat>", 0x1104],
    0x03139: ["<compat>", 0x1105],
    0x0313A: ["<compat>", 0x11B0],
    0x0313B: ["<compat>", 0x11B1],
    0x0313C: ["<compat>", 0x11B2],
    0x0313D: ["<compat>", 0x11B3],
    0x0313E: ["<compat>", 0x11B4],
    0x0313F: ["<compat>", 0x11B5],
    0x03140: ["<compat>", 0x111A],
    0x03141: ["<compat>", 0x1106],
    0x03142: ["<compat>", 0x1107],
    0x03143: ["<compat>", 0x1108],
    0x03144: ["<compat>", 0x1121],
    0x03145: ["<compat>", 0x1109],
    0x03146: ["<compat>", 0x110A],
    0x03147: ["<compat>", 0x110B],
    0x03148: ["<compat>", 0x110C],
    0x03149: ["<compat>", 0x110D],
    0x0314A: ["<compat>", 0x110E],
    0x0314B: ["<compat>", 0x110F],
    0x0314C: ["<compat>", 0x1110],
    0x0314D: ["<compat>", 0x1111],
    0x0314E: ["<compat>", 0x1112],
    0x0314F: ["<compat>", 0x1161],
    0x03150: ["<compat>", 0x1162],
    0x03151: ["<compat>", 0x1163],
    0x03152: ["<compat>", 0x1164],
    0x03153: ["<compat>", 0x1165],
    0x03154: ["<compat>", 0x1166],
    0x03155: ["<compat>", 0x1167],
    0x03156: ["<compat>", 0x1168],
    0x03157: ["<compat>", 0x1169],
    0x03158: ["<compat>", 0x116A],
    0x03159: ["<compat>", 0x116B],
    0x0315A: ["<compat>", 0x116C],
    0x0315B: ["<compat>", 0x116D],
    0x0315C: ["<compat>", 0x116E],
    0x0315D: ["<compat>", 0x116F],
    0x0315E: ["<compat>", 0x1170],
    0x0315F: ["<compat>", 0x1171],
    0x03160: ["<compat>", 0x1172],
    0x03161: ["<compat>", 0x1173],
    0x03162: ["<compat>", 0x1174],
    0x03163: ["<compat>", 0x1175],
    0x03164: ["<compat>", 0x1160],
    0x03165: ["<compat>", 0x1114],
    0x03166: ["<compat>", 0x1115],
    0x03167: ["<compat>", 0x11C7],
    0x03168: ["<compat>", 0x11C8],
    0x03169: ["<compat>", 0x11CC],
    0x0316A: ["<compat>", 0x11CE],
    0x0316B: ["<compat>", 0x11D3],
    0x0316C: ["<compat>", 0x11D7],
    0x0316D: ["<compat>", 0x11D9],
    0x0316E: ["<compat>", 0x111C],
    0x0316F: ["<compat>", 0x11DD],
    0x03170: ["<compat>", 0x11DF],
    0x03171: ["<compat>", 0x111D],
    0x03172: ["<compat>", 0x111E],
    0x03173: ["<compat>", 0x1120],
    0x03174: ["<compat>", 0x1122],
    0x03175: ["<compat>", 0x1123],
    0x03176: ["<compat>", 0x1127],
    0x03177: ["<compat>", 0x1129],
    0x03178: ["<compat>", 0x112B],
    0x03179: ["<compat>", 0x112C],
    0x0317A: ["<compat>", 0x112D],
    0x0317B: ["<compat>", 0x112E],
    0x0317C: ["<compat>", 0x112F],
    0x0317D: ["<compat>", 0x1132],
    0x0317E: ["<compat>", 0x1136],
    0x0317F: ["<compat>", 0x1140],
    0x03180: ["<compat>", 0x1147],
    0x03181: ["<compat>", 0x114C],
    0x03182: ["<compat>", 0x11F1],
    0x03183: ["<compat>", 0x11F2],
    0x03184: ["<compat>", 0x1157],
    0x03185: ["<compat>", 0x1158],
    0x03186: ["<compat>", 0x1159],
    0x03187: ["<compat>", 0x1184],
    0x03188: ["<compat>", 0x1185],
    0x03189: ["<compat>", 0x1188],
    0x0318A: ["<compat>", 0x1191],
    0x0318B: ["<compat>", 0x1192],
    0x0318C: ["<compat>", 0x1194],
    0x0318D: ["<compat>", 0x119E],
    0x0318E: ["<compat>", 0x11A1],
    0x03192: ["<super>", 0x4E00],
    0x03193: ["<super>", 0x4E8C],
    0x03194: ["<super>", 0x4E09],
    0x03195: ["<super>", 0x56DB],
    0x03196: ["<super>", 0x4E0A],
    0x03197: ["<super>", 0x4E2D],
    0x03198: ["<super>", 0x4E0B],
    0x03199: ["<super>", 0x7532],
    0x0319A: ["<super>", 0x4E59],
    0x0319B: ["<super>", 0x4E19],
    0x0319C: ["<super>", 0x4E01],
    0x0319D: ["<super>", 0x5929],
    0x0319E: ["<super>", 0x5730],
    0x0319F: ["<super>", 0x4EBA],
    0x03200: ["<compat>", 0x0028, 0x1100, 0x0029],
    0x03201: ["<compat>", 0x0028, 0x1102, 0x0029],
    0x03202: ["<compat>", 0x0028, 0x1103, 0x0029],
    0x03203: ["<compat>", 0x0028, 0x1105, 0x0029],
    0x03204: ["<compat>", 0x0028, 0x1106, 0x0029],
    0x03205: ["<compat>", 0x0028, 0x1107, 0x0029],
    0x03206: ["<compat>", 0x0028, 0x1109, 0x0029],
    0x03207: ["<compat>", 0x0028, 0x110B, 0x0029],
    0x03208: ["<compat>", 0x0028, 0x110C, 0x0029],
    0x03209: ["<compat>", 0x0028, 0x110E, 0x0029],
    0x0320A: ["<compat>", 0x0028, 0x110F, 0x0029],
    0x0320B: ["<compat>", 0x0028, 0x1110, 0x0029],
    0x0320C: ["<compat>", 0x0028, 0x1111, 0x0029],
    0x0320D: ["<compat>", 0x0028, 0x1112, 0x0029],
    0x0320E: ["<compat>", 0x0028, 0x1100, 0x1161, 0x0029],
    0x0320F: ["<compat>", 0x0028, 0x1102, 0x1161, 0x0029],
    0x03210: ["<compat>", 0x0028, 0x1103, 0x1161, 0x0029],
    0x03211: ["<compat>", 0x0028, 0x1105, 0x1161, 0x0029],
    0x03212: ["<compat>", 0x0028, 0x1106, 0x1161, 0x0029],
    0x03213: ["<compat>", 0x0028, 0x1107, 0x1161, 0x0029],
    0x03214: ["<compat>", 0x0028, 0x1109, 0x1161, 0x0029],
    0x03215: ["<compat>", 0x0028, 0x110B, 0x1161, 0x0029],
    0x03216: ["<compat>", 0x0028, 0x110C, 0x1161, 0x0029],
    0x03217: ["<compat>", 0x0028, 0x110E, 0x1161, 0x0029],
    0x03218: ["<compat>", 0x0028, 0x110F, 0x1161, 0x0029],
    0x03219: ["<compat>", 0x0028, 0x1110, 0x1161, 0x0029],
    0x0321A: ["<compat>", 0x0028, 0x1111, 0x1161, 0x0029],
    0x0321B: ["<compat>", 0x0028, 0x1112, 0x1161, 0x0029],
    0x0321C: ["<compat>", 0x0028, 0x110C, 0x116E, 0x0029],
    0x0321D: ["<compat>", 0x0028, 0x110B, 0x1169, 0x110C, 0x1165, 0x11AB, 0x0029],
    0x0321E: ["<compat>", 0x0028, 0x110B, 0x1169, 0x1112, 0x116E, 0x0029],
    0x03220: ["<compat>", 0x0028, 0x4E00, 0x0029],
    0x03221: ["<compat>", 0x0028, 0x4E8C, 0x0029],
    0x03222: ["<compat>", 0x0028, 0x4E09, 0x0029],
    0x03223: ["<compat>", 0x0028, 0x56DB, 0x0029],
    0x03224: ["<compat>", 0x0028, 0x4E94, 0x0029],
    0x03225: ["<compat>", 0x0028, 0x516D, 0x0029],
    0x03226: ["<compat>", 0x0028, 0x4E03, 0x0029],
    0x03227: ["<compat>", 0x0028, 0x516B, 0x0029],
    0x03228: ["<compat>", 0x0028, 0x4E5D, 0x0029],
    0x03229: ["<compat>", 0x0028, 0x5341, 0x0029],
    0x0322A: ["<compat>", 0x0028, 0x6708, 0x0029],
    0x0322B: ["<compat>", 0x0028, 0x706B, 0x0029],
    0x0322C: ["<compat>", 0x0028, 0x6C34, 0x0029],
    0x0322D: ["<compat>", 0x0028, 0x6728, 0x0029],
    0x0322E: ["<compat>", 0x0028, 0x91D1, 0x0029],
    0x0322F: ["<compat>", 0x0028, 0x571F, 0x0029],
    0x03230: ["<compat>", 0x0028, 0x65E5, 0x0029],
    0x03231: ["<compat>", 0x0028, 0x682A, 0x0029],
    0x03232: ["<compat>", 0x0028, 0x6709, 0x0029],
    0x03233: ["<compat>", 0x0028, 0x793E, 0x0029],
    0x03234: ["<compat>", 0x0028, 0x540D, 0x0029],
    0x03235: ["<compat>", 0x0028, 0x7279, 0x0029],
    0x03236: ["<compat>", 0x0028, 0x8CA1, 0x0029],
    0x03237: ["<compat>", 0x0028, 0x795D, 0x0029],
    0x03238: ["<compat>", 0x0028, 0x52B4, 0x0029],
    0x03239: ["<compat>", 0x0028, 0x4EE3, 0x0029],
    0x0323A: ["<compat>", 0x0028, 0x547C, 0x0029],
    0x0323B: ["<compat>", 0x0028, 0x5B66, 0x0029],
    0x0323C: ["<compat>", 0x0028, 0x76E3, 0x0029],
    0x0323D: ["<compat>", 0x0028, 0x4F01, 0x0029],
    0x0323E: ["<compat>", 0x0028, 0x8CC7, 0x0029],
    0x0323F: ["<compat>", 0x0028, 0x5354, 0x0029],
    0x03240: ["<compat>", 0x0028, 0x796D, 0x0029],
    0x03241: ["<compat>", 0x0028, 0x4F11, 0x0029],
    0x03242: ["<compat>", 0x0028, 0x81EA, 0x0029],
    0x03243: ["<compat>", 0x0028, 0x81F3, 0x0029],
    0x03244: ["<circle>", 0x554F],
    0x03245: ["<circle>", 0x5E7C],
    0x03246: ["<circle>", 0x6587],
    0x03247: ["<circle>", 0x7B8F],
    0x03250: ["<square>", 0x0050, 0x0054, 0x0045],
    0x03251: ["<circle>", 0x0032, 0x0031],
    0x03252: ["<circle>", 0x0032, 0x0032],
    0x03253: ["<circle>", 0x0032, 0x0033],
    0x03254: ["<circle>", 0x0032, 0x0034],
    0x03255: ["<circle>", 0x0032, 0x0035],
    0x03256: ["<circle>", 0x0032, 0x0036],
    0x03257: ["<circle>", 0x0032, 0x0037],
    0x03258: ["<circle>", 0x0032, 0x0038],
    0x03259: ["<circle>", 0x0032, 0x0039],
    0x0325A: ["<circle>", 0x0033, 0x0030],
    0x0325B: ["<circle>", 0x0033, 0x0031],
    0x0325C: ["<circle>", 0x0033, 0x0032],
    0x0325D: ["<circle>", 0x0033, 0x0033],
    0x0325E: ["<circle>", 0x0033, 0x0034],
    0x0325F: ["<circle>", 0x0033, 0x0035],
    0x03260: ["<circle>", 0x1100],
    0x03261: ["<circle>", 0x1102],
    0x03262: ["<circle>", 0x1103],
    0x03263: ["<circle>", 0x1105],
    0x03264: ["<circle>", 0x1106],
    0x03265: ["<circle>", 0x1107],
    0x03266: ["<circle>", 0x1109],
    0x03267: ["<circle>", 0x110B],
    0x03268: ["<circle>", 0x110C],
    0x03269: ["<circle>", 0x110E],
    0x0326A: ["<circle>", 0x110F],
    0x0326B: ["<circle>", 0x1110],
    0x0326C: ["<circle>", 0x1111],
    0x0326D: ["<circle>", 0x1112],
    0x0326E: ["<circle>", 0x1100, 0x1161],
    0x0326F: ["<circle>", 0x1102, 0x1161],
    0x03270: ["<circle>", 0x1103, 0x1161],
    0x03271: ["<circle>", 0x1105, 0x1161],
    0x03272: ["<circle>", 0x1106, 0x1161],
    0x03273: ["<circle>", 0x1107, 0x1161],
    0x03274: ["<circle>", 0x1109, 0x1161],
    0x03275: ["<circle>", 0x110B, 0x1161],
    0x03276: ["<circle>", 0x110C, 0x1161],
    0x03277: ["<circle>", 0x110E, 0x1161],
    0x03278: ["<circle>", 0x110F, 0x1161],
    0x03279: ["<circle>", 0x1110, 0x1161],
    0x0327A: ["<circle>", 0x1111, 0x1161],
    0x0327B: ["<circle>", 0x1112, 0x1161],
    0x0327C: ["<circle>", 0x110E, 0x1161, 0x11B7, 0x1100, 0x1169],
    0x0327D: ["<circle>", 0x110C, 0x116E, 0x110B, 0x1174],
    0x0327E: ["<circle>", 0x110B, 0x116E],
    0x03280: ["<circle>", 0x4E00],
    0x03281: ["<circle>", 0x4E8C],
    0x03282: ["<circle>", 0x4E09],
    0x03283: ["<circle>", 0x56DB],
    0x03284: ["<circle>", 0x4E94],
    0x03285: ["<circle>", 0x516D],
    0x03286: ["<circle>", 0x4E03],
    0x03287: ["<circle>", 0x516B],
    0x03288: ["<circle>", 0x4E5D],
    0x03289: ["<circle>", 0x5341],
    0x0328A: ["<circle>", 0x6708],
    0x0328B: ["<circle>", 0x706B],
    0x0328C: ["<circle>", 0x6C34],
    0x0328D: ["<circle>", 0x6728],
    0x0328E: ["<circle>", 0x91D1],
    0x0328F: ["<circle>", 0x571F],
    0x03290: ["<circle>", 0x65E5],
    0x03291: ["<circle>", 0x682A],
    0x03292: ["<circle>", 0x6709],
    0x03293: ["<circle>", 0x793E],
    0x03294: ["<circle>", 0x540D],
    0x03295: ["<circle>", 0x7279],
    0x03296: ["<circle>", 0x8CA1],
    0x03297: ["<circle>", 0x795D],
    0x03298: ["<circle>", 0x52B4],
    0x03299: ["<circle>", 0x79D8],
    0x0329A: ["<circle>", 0x7537],
    0x0329B: ["<circle>", 0x5973],
    0x0329C: ["<circle>", 0x9069],
    0x0329D: ["<circle>", 0x512A],
    0x0329E: ["<circle>", 0x5370],
    0x0329F: ["<circle>", 0x6CE8],
    0x032A0: ["<circle>", 0x9805],
    0x032A1: ["<circle>", 0x4F11],
    0x032A2: ["<circle>", 0x5199],
    0x032A3: ["<circle>", 0x6B63],
    0x032A4: ["<circle>", 0x4E0A],
    0x032A5: ["<circle>", 0x4E2D],
    0x032A6: ["<circle>", 0x4E0B],
    0x032A7: ["<circle>", 0x5DE6],
    0x032A8: ["<circle>", 0x53F3],
    0x032A9: ["<circle>", 0x533B],
    0x032AA: ["<circle>", 0x5B97],
    0x032AB: ["<circle>", 0x5B66],
    0x032AC: ["<circle>", 0x76E3],
    0x032AD: ["<circle>", 0x4F01],
    0x032AE: ["<circle>", 0x8CC7],
    0x032AF: ["<circle>", 0x5354],
    0x032B0: ["<circle>", 0x591C],
    0x032B1: ["<circle>", 0x0033, 0x0036],
    0x032B2: ["<circle>", 0x0033, 0x0037],
    0x032B3: ["<circle>", 0x0033, 0x0038],
    0x032B4: ["<circle>", 0x0033, 0x0039],
    0x032B5: ["<circle>", 0x0034, 0x0030],
    0x032B6: ["<circle>", 0x0034, 0x0031],
    0x032B7: ["<circle>", 0x0034, 0x0032],
    0x032B8: ["<circle>", 0x0034, 0x0033],
    0x032B9: ["<circle>", 0x0034, 0x0034],
    0x032BA: ["<circle>", 0x0034, 0x0035],
    0x032BB: ["<circle>", 0x0034, 0x0036],
    0x032BC: ["<circle>", 0x0034, 0x0037],
    0x032BD: ["<circle>", 0x0034, 0x0038],
    0x032BE: ["<circle>", 0x0034, 0x0039],
    0x032BF: ["<circle>", 0x0035, 0x0030],
    0x032C0: ["<compat>", 0x0031, 0x6708],
    0x032C1: ["<compat>", 0x0032, 0x6708],
    0x032C2: ["<compat>", 0x0033, 0x6708],
    0x032C3: ["<compat>", 0x0034, 0x6708],
    0x032C4: ["<compat>", 0x0035, 0x6708],
    0x032C5: ["<compat>", 0x0036, 0x6708],
    0x032C6: ["<compat>", 0x0037, 0x6708],
    0x032C7: ["<compat>", 0x0038, 0x6708],
    0x032C8: ["<compat>", 0x0039, 0x6708],
    0x032C9: ["<compat>", 0x0031, 0x0030, 0x6708],
    0x032CA: ["<compat>", 0x0031, 0x0031, 0x6708],
    0x032CB: ["<compat>", 0x0031, 0x0032, 0x6708],
    0x032CC: ["<square>", 0x0048, 0x0067],
    0x032CD: ["<square>", 0x0065, 0x0072, 0x0067],
    0x032CE: ["<square>", 0x0065, 0x0056],
    0x032CF: ["<square>", 0x004C, 0x0054, 0x0044],
    0x032D0: ["<circle>", 0x30A2],
    0x032D1: ["<circle>", 0x30A4],
    0x032D2: ["<circle>", 0x30A6],
    0x032D3: ["<circle>", 0x30A8],
    0x032D4: ["<circle>", 0x30AA],
    0x032D5: ["<circle>", 0x30AB],
    0x032D6: ["<circle>", 0x30AD],
    0x032D7: ["<circle>", 0x30AF],
    0x032D8: ["<circle>", 0x30B1],
    0x032D9: ["<circle>", 0x30B3],
    0x032DA: ["<circle>", 0x30B5],
    0x032DB: ["<circle>", 0x30B7],
    0x032DC: ["<circle>", 0x30B9],
    0x032DD: ["<circle>", 0x30BB],
    0x032DE: ["<circle>", 0x30BD],
    0x032DF: ["<circle>", 0x30BF],
    0x032E0: ["<circle>", 0x30C1],
    0x032E1: ["<circle>", 0x30C4],
    0x032E2: ["<circle>", 0x30C6],
    0x032E3: ["<circle>", 0x30C8],
    0x032E4: ["<circle>", 0x30CA],
    0x032E5: ["<circle>", 0x30CB],
    0x032E6: ["<circle>", 0x30CC],
    0x032E7: ["<circle>", 0x30CD],
    0x032E8: ["<circle>", 0x30CE],
    0x032E9: ["<circle>", 0x30CF],
    0x032EA: ["<circle>", 0x30D2],
    0x032EB: ["<circle>", 0x30D5],
    0x032EC: ["<circle>", 0x30D8],
    0x032ED: ["<circle>", 0x30DB],
    0x032EE: ["<circle>", 0x30DE],
    0x032EF: ["<circle>", 0x30DF],
    0x032F0: ["<circle>", 0x30E0],
    0x032F1: ["<circle>", 0x30E1],
    0x032F2: ["<circle>", 0x30E2],
    0x032F3: ["<circle>", 0x30E4],
    0x032F4: ["<circle>", 0x30E6],
    0x032F5: ["<circle>", 0x30E8],
    0x032F6: ["<circle>", 0x30E9],
    0x032F7: ["<circle>", 0x30EA],
    0x032F8: ["<circle>", 0x30EB],
    0x032F9: ["<circle>", 0x30EC],
    0x032FA: ["<circle>", 0x30ED],
    0x032FB: ["<circle>", 0x30EF],
    0x032FC: ["<circle>", 0x30F0],
    0x032FD: ["<circle>", 0x30F1],
    0x032FE: ["<circle>", 0x30F2],
    0x032FF: ["<square>", 0x4EE4, 0x548C],
    0x03300: ["<square>", 0x30A2, 0x30D1, 0x30FC, 0x30C8],
    0x03301: ["<square>", 0x30A2, 0x30EB, 0x30D5, 0x30A1],
    0x03302: ["<square>", 0x30A2, 0x30F3, 0x30DA, 0x30A2],
    0x03303: ["<square>", 0x30A2, 0x30FC, 0x30EB],
    0x03304: ["<square>", 0x30A4, 0x30CB, 0x30F3, 0x30B0],
    0x03305: ["<square>", 0x30A4, 0x30F3, 0x30C1],
    0x03306: ["<square>", 0x30A6, 0x30A9, 0x30F3],
    0x03307: ["<square>", 0x30A8, 0x30B9, 0x30AF, 0x30FC, 0x30C9],
    0x03308: ["<square>", 0x30A8, 0x30FC, 0x30AB, 0x30FC],
    0x03309: ["<square>", 0x30AA, 0x30F3, 0x30B9],
    0x0330A: ["<square>", 0x30AA, 0x30FC, 0x30E0],
    0x0330B: ["<square>", 0x30AB, 0x30A4, 0x30EA],
    0x0330C: ["<square>", 0x30AB, 0x30E9, 0x30C3, 0x30C8],
    0x0330D: ["<square>", 0x30AB, 0x30ED, 0x30EA, 0x30FC],
    0x0330E: ["<square>", 0x30AC, 0x30ED, 0x30F3],
    0x0330F: ["<square>", 0x30AC, 0x30F3, 0x30DE],
    0x03310: ["<square>", 0x30AE, 0x30AC],
    0x03311: ["<square>", 0x30AE, 0x30CB, 0x30FC],
    0x03312: ["<square>", 0x30AD, 0x30E5, 0x30EA, 0x30FC],
    0x03313: ["<square>", 0x30AE, 0x30EB, 0x30C0, 0x30FC],
    0x03314: ["<square>", 0x30AD, 0x30ED],
    0x03315: ["<square>", 0x30AD, 0x30ED, 0x30B0, 0x30E9, 0x30E0],
    0x03316: ["<square>", 0x30AD, 0x30ED, 0x30E1, 0x30FC, 0x30C8, 0x30EB],
    0x03317: ["<square>", 0x30AD, 0x30ED, 0x30EF, 0x30C3, 0x30C8],
    0x03318: ["<square>", 0x30B0, 0x30E9, 0x30E0],
    0x03319: ["<square>", 0x30B0, 0x30E9, 0x30E0, 0x30C8, 0x30F3],
    0x0331A: ["<square>", 0x30AF, 0x30EB, 0x30BC, 0x30A4, 0x30ED],
    0x0331B: ["<square>", 0x30AF, 0x30ED, 0x30FC, 0x30CD],
    0x0331C: ["<square>", 0x30B1, 0x30FC, 0x30B9],
    0x0331D: ["<square>", 0x30B3, 0x30EB, 0x30CA],
    0x0331E: ["<square>", 0x30B3, 0x30FC, 0x30DD],
    0x0331F: ["<square>", 0x30B5, 0x30A4, 0x30AF, 0x30EB],
    0x03320: ["<square>", 0x30B5, 0x30F3, 0x30C1, 0x30FC, 0x30E0],
    0x03321: ["<square>", 0x30B7, 0x30EA, 0x30F3, 0x30B0],
    0x03322: ["<square>", 0x30BB, 0x30F3, 0x30C1],
    0x03323: ["<square>", 0x30BB, 0x30F3, 0x30C8],
    0x03324: ["<square>", 0x30C0, 0x30FC, 0x30B9],
    0x03325: ["<square>", 0x30C7, 0x30B7],
    0x03326: ["<square>", 0x30C9, 0x30EB],
    0x03327: ["<square>", 0x30C8, 0x30F3],
    0x03328: ["<square>", 0x30CA, 0x30CE],
    0x03329: ["<square>", 0x30CE, 0x30C3, 0x30C8],
    0x0332A: ["<square>", 0x30CF, 0x30A4, 0x30C4],
    0x0332B: ["<square>", 0x30D1, 0x30FC, 0x30BB, 0x30F3, 0x30C8],
    0x0332C: ["<square>", 0x30D1, 0x30FC, 0x30C4],
    0x0332D: ["<square>", 0x30D0, 0x30FC, 0x30EC, 0x30EB],
    0x0332E: ["<square>", 0x30D4, 0x30A2, 0x30B9, 0x30C8, 0x30EB],
    0x0332F: ["<square>", 0x30D4, 0x30AF, 0x30EB],
    0x03330: ["<square>", 0x30D4, 0x30B3],
    0x03331: ["<square>", 0x30D3, 0x30EB],
    0x03332: ["<square>", 0x30D5, 0x30A1, 0x30E9, 0x30C3, 0x30C9],
    0x03333: ["<square>", 0x30D5, 0x30A3, 0x30FC, 0x30C8],
    0x03334: ["<square>", 0x30D6, 0x30C3, 0x30B7, 0x30A7, 0x30EB],
    0x03335: ["<square>", 0x30D5, 0x30E9, 0x30F3],
    0x03336: ["<square>", 0x30D8, 0x30AF, 0x30BF, 0x30FC, 0x30EB],
    0x03337: ["<square>", 0x30DA, 0x30BD],
    0x03338: ["<square>", 0x30DA, 0x30CB, 0x30D2],
    0x03339: ["<square>", 0x30D8, 0x30EB, 0x30C4],
    0x0333A: ["<square>", 0x30DA, 0x30F3, 0x30B9],
    0x0333B: ["<square>", 0x30DA, 0x30FC, 0x30B8],
    0x0333C: ["<square>", 0x30D9, 0x30FC, 0x30BF],
    0x0333D: ["<square>", 0x30DD, 0x30A4, 0x30F3, 0x30C8],
    0x0333E: ["<square>", 0x30DC, 0x30EB, 0x30C8],
    0x0333F: ["<square>", 0x30DB, 0x30F3],
    0x03340: ["<square>", 0x30DD, 0x30F3, 0x30C9],
    0x03341: ["<square>", 0x30DB, 0x30FC, 0x30EB],
    0x03342: ["<square>", 0x30DB, 0x30FC, 0x30F3],
    0x03343: ["<square>", 0x30DE, 0x30A4, 0x30AF, 0x30ED],
    0x03344: ["<square>", 0x30DE, 0x30A4, 0x30EB],
    0x03345: ["<square>", 0x30DE, 0x30C3, 0x30CF],
    0x03346: ["<square>", 0x30DE, 0x30EB, 0x30AF],
    0x03347: ["<square>", 0x30DE, 0x30F3, 0x30B7, 0x30E7, 0x30F3],
    0x03348: ["<square>", 0x30DF, 0x30AF, 0x30ED, 0x30F3],
    0x03349: ["<square>", 0x30DF, 0x30EA],
    0x0334A: ["<square>", 0x30DF, 0x30EA, 0x30D0, 0x30FC, 0x30EB],
    0x0334B: ["<square>", 0x30E1, 0x30AC],
    0x0334C: ["<square>", 0x30E1, 0x30AC, 0x30C8, 0x30F3],
    0x0334D: ["<square>", 0x30E1, 0x30FC, 0x30C8, 0x30EB],
    0x0334E: ["<square>", 0x30E4, 0x30FC, 0x30C9],
    0x0334F: ["<square>", 0x30E4, 0x30FC, 0x30EB],
    0x03350: ["<square>", 0x30E6, 0x30A2, 0x30F3],
    0x03351: ["<square>", 0x30EA, 0x30C3, 0x30C8, 0x30EB],
    0x03352: ["<square>", 0x30EA, 0x30E9],
    0x03353: ["<square>", 0x30EB, 0x30D4, 0x30FC],
    0x03354: ["<square>", 0x30EB, 0x30FC, 0x30D6, 0x30EB],
    0x03355: ["<square>", 0x30EC, 0x30E0],
    0x03356: ["<square>", 0x30EC, 0x30F3, 0x30C8, 0x30B2, 0x30F3],
    0x03357: ["<square>", 0x30EF, 0x30C3, 0x30C8],
    0x03358: ["<compat>", 0x0030, 0x70B9],
    0x03359: ["<compat>", 0x0031, 0x70B9],
    0x0335A: ["<compat>", 0x0032, 0x70B9],
    0x0335B: ["<compat>", 0x0033, 0x70B9],
    0x0335C: ["<compat>", 0x0034, 0x70B9],
    0x0335D: ["<compat>", 0x0035, 0x70B9],
    0x0335E: ["<compat>", 0x0036, 0x70B9],
    0x0335F: ["<compat>", 0x0037, 0x70B9],
    0x03360: ["<compat>", 0x0038, 0x70B9],
    0x03361: ["<compat>", 0x0039, 0x70B9],
    0x03362: ["<compat>", 0x0031, 0x0030, 0x70B9],
    0x03363: ["<compat>", 0x0031, 0x0031, 0x70B9],
    0x03364: ["<compat>", 0x0031, 0x0032, 0x70B9],
    0x03365: ["<compat>", 0x0031, 0x0033, 0x70B9],
    0x03366: ["<compat>", 0x0031, 0x0034, 0x70B9],
    0x03367: ["<compat>", 0x0031, 0x0035, 0x70B9],
    0x03368: ["<compat>", 0x0031, 0x0036, 0x70B9],
    0x03369: ["<compat>", 0x0031, 0x0037, 0x70B9],
    0x0336A: ["<compat>", 0x0031, 0x0038, 0x70B9],
    0x0336B: ["<compat>", 0x0031, 0x0039, 0x70B9],
    0x0336C: ["<compat>", 0x0032, 0x0030, 0x70B9],
    0x0336D: ["<compat>", 0x0032, 0x0031, 0x70B9],
    0x0336E: ["<compat>", 0x0032, 0x0032, 0x70B9],
    0x0336F: ["<compat>", 0x0032, 0x0033, 0x70B9],
    0x03370: ["<compat>", 0x0032, 0x0034, 0x70B9],
    0x03371: ["<square>", 0x0068, 0x0050, 0x0061],
    0x03372: ["<square>", 0x0064, 0x0061],
    0x03373: ["<square>", 0x0041, 0x0055],
    0x03374: ["<square>", 0x0062, 0x0061, 0x0072],
    0x03375: ["<square>", 0x006F, 0x0056],
    0x03376: ["<square>", 0x0070, 0x0063],
    0x03377: ["<square>", 0x0064, 0x006D],
    0x03378: ["<square>", 0x0064, 0x006D, 0x00B2],
    0x03379: ["<square>", 0x0064, 0x006D, 0x00B3],
    0x0337A: ["<square>", 0x0049, 0x0055],
    0x0337B: ["<square>", 0x5E73, 0x6210],
    0x0337C: ["<square>", 0x662D, 0x548C],
    0x0337D: ["<square>", 0x5927, 0x6B63],
    0x0337E: ["<square>", 0x660E, 0x6CBB],
    0x0337F: ["<square>", 0x682A, 0x5F0F, 0x4F1A, 0x793E],
    0x03380: ["<square>", 0x0070, 0x0041],
    0x03381: ["<square>", 0x006E, 0x0041],
    0x03382: ["<square>", 0x03BC, 0x0041],
    0x03383: ["<square>", 0x006D, 0x0041],
    0x03384: ["<square>", 0x006B, 0x0041],
    0x03385: ["<square>", 0x004B, 0x0042],
    0x03386: ["<square>", 0x004D, 0x0042],
    0x03387: ["<square>", 0x0047, 0x0042],
    0x03388: ["<square>", 0x0063, 0x0061, 0x006C],
    0x03389: ["<square>", 0x006B, 0x0063, 0x0061, 0x006C],
    0x0338A: ["<square>", 0x0070, 0x0046],
    0x0338B: ["<square>", 0x006E, 0x0046],
    0x0338C: ["<square>", 0x03BC, 0x0046],
    0x0338D: ["<square>", 0x03BC, 0x0067],
    0x0338E: ["<square>", 0x006D, 0x0067],
    0x0338F: ["<square>", 0x006B, 0x0067],
    0x03390: ["<square>", 0x0048, 0x007A],
    0x03391: ["<square>", 0x006B, 0x0048, 0x007A],
    0x03392: ["<square>", 0x004D, 0x0048, 0x007A],
    0x03393: ["<square>", 0x0047, 0x0048, 0x007A],
    0x03394: ["<square>", 0x0054, 0x0048, 0x007A],
    0x03395: ["<square>", 0x03BC, 0x2113],
    0x03396: ["<square>", 0x006D, 0x2113],
    0x03397: ["<square>", 0x0064, 0x2113],
    0x03398: ["<square>", 0x006B, 0x2113],
    0x03399: ["<square>", 0x0066, 0x006D],
    0x0339A: ["<square>", 0x006E, 0x006D],
    0x0339B: ["<square>", 0x03BC, 0x006D],
    0x0339C: ["<square>", 0x006D, 0x006D],
    0x0339D: ["<square>", 0x0063, 0x006D],
    0x0339E: ["<square>", 0x006B, 0x006D],
    0x0339F: ["<square>", 0x006D, 0x006D, 0x00B2],
    0x033A0: ["<square>", 0x0063, 0x006D, 0x00B2],
    0x033A1: ["<square>", 0x006D, 0x00B2],
    0x033A2: ["<square>", 0x006B, 0x006D, 0x00B2],
    0x033A3: ["<square>", 0x006D, 0x006D, 0x00B3],
    0x033A4: ["<square>", 0x0063, 0x006D, 0x00B3],
    0x033A5: ["<square>", 0x006D, 0x00B3],
    0x033A6: ["<square>", 0x006B, 0x006D, 0x00B3],
    0x033A7: ["<square>", 0x006D, 0x2215, 0x0073],
    0x033A8: ["<square>", 0x006D, 0x2215, 0x0073, 0x00B2],
    0x033A9: ["<square>", 0x0050, 0x0061],
    0x033AA: ["<square>", 0x006B, 0x0050, 0x0061],
    0x033AB: ["<square>", 0x004D, 0x0050, 0x0061],
    0x033AC: ["<square>", 0x0047, 0x0050, 0x0061],
    0x033AD: ["<square>", 0x0072, 0x0061, 0x0064],
    0x033AE: ["<square>", 0x0072, 0x0061, 0x0064, 0x2215, 0x0073],
    0x033AF: ["<square>", 0x0072, 0x0061, 0x0064, 0x2215, 0x0073, 0x00B2],
    0x033B0: ["<square>", 0x0070, 0x0073],
    0x033B1: ["<square>", 0x006E, 0x0073],
    0x033B2: ["<square>", 0x03BC, 0x0073],
    0x033B3: ["<square>", 0x006D, 0x0073],
    0x033B4: ["<square>", 0x0070, 0x0056],
    0x033B5: ["<square>", 0x006E, 0x0056],
    0x033B6: ["<square>", 0x03BC, 0x0056],
    0x033B7: ["<square>", 0x006D, 0x0056],
    0x033B8: ["<square>", 0x006B, 0x0056],
    0x033B9: ["<square>", 0x004D, 0x0056],
    0x033BA: ["<square>", 0x0070, 0x0057],
    0x033BB: ["<square>", 0x006E, 0x0057],
    0x033BC: ["<square>", 0x03BC, 0x0057],
    0x033BD: ["<square>", 0x006D, 0x0057],
    0x033BE: ["<square>", 0x006B, 0x0057],
    0x033BF: ["<square>", 0x004D, 0x0057],
    0x033C0: ["<square>", 0x006B, 0x03A9],
    0x033C1: ["<square>", 0x004D, 0x03A9],
    0x033C2: ["<square>", 0x0061, 0x002E, 0x006D, 0x002E],
    0x033C3: ["<square>", 0x0042, 0x0071],
    0x033C4: ["<square>", 0x0063, 0x0063],
    0x033C5: ["<square>", 0x0063, 0x0064],
    0x033C6: ["<square>", 0x0043, 0x2215, 0x006B, 0x0067],
    0x033C7: ["<square>", 0x0043, 0x006F, 0x002E],
    0x033C8: ["<square>", 0x0064, 0x0042],
    0x033C9: ["<square>", 0x0047, 0x0079],
    0x033CA: ["<square>", 0x0068, 0x0061],
    0x033CB: ["<square>", 0x0048, 0x0050],
    0x033CC: ["<square>", 0x0069, 0x006E],
    0x033CD: ["<square>", 0x004B, 0x004B],
    0x033CE: ["<square>", 0x004B, 0x004D],
    0x033CF: ["<square>", 0x006B, 0x0074],
    0x033D0: ["<square>", 0x006C, 0x006D],
    0x033D1: ["<square>", 0x006C, 0x006E],
    0x033D2: ["<square>", 0x006C, 0x006F, 0x0067],
    0x033D3: ["<square>", 0x006C, 0x0078],
    0x033D4: ["<square>", 0x006D, 0x0062],
    0x033D5: ["<square>", 0x006D, 0x0069, 0x006C],
    0x033D6: ["<square>", 0x006D, 0x006F, 0x006C],
    0x033D7: ["<square>", 0x0050, 0x0048],
    0x033D8: ["<square>", 0x0070, 0x002E, 0x006D, 0x002E],
    0x033D9: ["<square>", 0x0050, 0x0050, 0x004D],
    0x033DA: ["<square>", 0x0050, 0x0052],
    0x033DB: ["<square>", 0x0073, 0x0072],
    0x033DC: ["<square>", 0x0053, 0x0076],
    0x033DD: ["<square>", 0x0057, 0x0062],
    0x033DE: ["<square>", 0x0056, 0x2215, 0x006D],
    0x033DF: ["<square>", 0x0041, 0x2215, 0x006D],
    0x033E0: ["<compat>", 0x0031, 0x65E5],
    0x033E1: ["<compat>", 0x0032, 0x65E5],
    0x033E2: ["<compat>", 0x0033, 0x65E5],
    0x033E3: ["<compat>", 0x0034, 0x65E5],
    0x033E4: ["<compat>", 0x0035, 0x65E5],
    0x033E5: ["<compat>", 0x0036, 0x65E5],
    0x033E6: ["<compat>", 0x0037, 0x65E5],
    0x033E7: ["<compat>", 0x0038, 0x65E5],
    0x033E8: ["<compat>", 0x0039, 0x65E5],
    0x033E9: ["<compat>", 0x0031, 0x0030, 0x65E5],
    0x033EA: ["<compat>", 0x0031, 0x0031, 0x65E5],
    0x033EB: ["<compat>", 0x0031, 0x0032, 0x65E5],
    0x033EC: ["<compat>", 0x0031, 0x0033, 0x65E5],
    0x033ED: ["<compat>", 0x0031, 0x0034, 0x65E5],
    0x033EE: ["<compat>", 0x0031, 0x0035, 0x65E5],
    0x033EF: ["<compat>", 0x0031, 0x0036, 0x65E5],
    0x033F0: ["<compat>", 0x0031, 0x0037, 0x65E5],
    0x033F1: ["<compat>", 0x0031, 0x0038, 0x65E5],
    0x033F2: ["<compat>", 0x0031, 0x0039, 0x65E5],
    0x033F3: ["<compat>", 0x0032, 0x0030, 0x65E5],
    0x033F4: ["<compat>", 0x0032, 0x0031, 0x65E5],
    0x033F5: ["<compat>", 0x0032, 0x0032, 0x65E5],
    0x033F6: ["<compat>", 0x0032, 0x0033, 0x65E5],
    0x033F7: ["<compat>", 0x0032, 0x0034, 0x65E5],
    0x033F8: ["<compat>", 0x0032, 0x0035, 0x65E5],
    0x033F9: ["<compat>", 0x0032, 0x0036, 0x65E5],
    0x033FA: ["<compat>", 0x0032, 0x0037, 0x65E5],
    0x033FB: ["<compat>", 0x0032, 0x0038, 0x65E5],
    0x033FC: ["<compat>", 0x0032, 0x0039, 0x65E5],
    0x033FD: ["<compat>", 0x0033, 0x0030, 0x65E5],
    0x033FE: ["<compat>", 0x0033, 0x0031, 0x65E5],
    0x033FF: ["<square>", 0x0067, 0x0061, 0x006C],
    0x0A69C: ["<super>", 0x044A],
    0x0A69D: ["<super>", 0x044C],
    0x0A770: ["<super>", 0xA76F],
    0x0A7F2: ["<super>", 0x0043],
    0x0A7F3: ["<super>", 0x0046],
    0x0A7F4: ["<super>", 0x0051],
    0x0A7F8: ["<super>", 0x0126],
    0x0A7F9: ["<super>", 0x0153],
    0x0AB5C: ["<super>", 0xA727],
    0x0AB5D: ["<super>", 0xAB37],
    0x0AB5E: ["<super>", 0x026B],
    0x0AB5F: ["<super>", 0xAB52],
    0x0AB69: ["<super>", 0x028D],
    0x0F900: [0x8C48],
    0x0F901: [0x66F4],
    0x0F902: [0x8ECA],
    0x0F903: [0x8CC8],
    0x0F904: [0x6ED1],
    0x0F905: [0x4E32],
    0x0F906: [0x53E5],
    0x0F907: [0x9F9C],
    0x0F908: [0x9F9C],
    0x0F909: [0x5951],
    0x0F90A: [0x91D1],
    0x0F90B: [0x5587],
    0x0F90C: [0x5948],
    0x0F90D: [0x61F6],
    0x0F90E: [0x7669],
    0x0F90F: [0x7F85],
    0x0F910: [0x863F],
    0x0F911: [0x87BA],
    0x0F912: [0x88F8],
    0x0F913: [0x908F],
    0x0F914: [0x6A02],
    0x0F915: [0x6D1B],
    0x0F916: [0x70D9],
    0x0F917: [0x73DE],
    0x0F918: [0x843D],
    0x0F919: [0x916A],
    0x0F91A: [0x99F1],
    0x0F91B: [0x4E82],
    0x0F91C: [0x5375],
    0x0F91D: [0x6B04],
    0x0F91E: [0x721B],
    0x0F91F: [0x862D],
    0x0F920: [0x9E1E],
    0x0F921: [0x5D50],
    0x0F922: [0x6FEB],
    0x0F923: [0x85CD],
    0x0F924: [0x8964],
    0x0F925: [0x62C9],
    0x0F926: [0x81D8],
    0x0F927: [0x881F],
    0x0F928: [0x5ECA],
    0x0F929: [0x6717],
    0x0F92A: [0x6D6A],
    0x0F92B: [0x72FC],
    0x0F92C: [0x90CE],
    0x0F92D: [0x4F86],
    0x0F92E: [0x51B7],
    0x0F92F: [0x52DE],
    0x0F930: [0x64C4],
    0x0F931: [0x6AD3],
    0x0F932: [0x7210],
    0x0F933: [0x76E7],
    0x0F934: [0x8001],
    0x0F935: [0x8606],
    0x0F936: [0x865C],
    0x0F937: [0x8DEF],
    0x0F938: [0x9732],
    0x0F939: [0x9B6F],
    0x0F93A: [0x9DFA],
    0x0F93B: [0x788C],
    0x0F93C: [0x797F],
    0x0F93D: [0x7DA0],
    0x0F93E: [0x83C9],
    0x0F93F: [0x9304],
    0x0F940: [0x9E7F],
    0x0F941: [0x8AD6],
    0x0F942: [0x58DF],
    0x0F943: [0x5F04],
    0x0F944: [0x7C60],
    0x0F945: [0x807E],
    0x0F946: [0x7262],
    0x0F947: [0x78CA],
    0x0F948: [0x8CC2],
    0x0F949: [0x96F7],
    0x0F94A: [0x58D8],
    0x0F94B: [0x5C62],
    0x0F94C: [0x6A13],
    0x0F94D: [0x6DDA],
    0x0F94E: [0x6F0F],
    0x0F94F: [0x7D2F],
    0x0F950: [0x7E37],
    0x0F951: [0x964B],
    0x0F952: [0x52D2],
    0x0F953: [0x808B],
    0x0F954: [0x51DC],
    0x0F955: [0x51CC],
    0x0F956: [0x7A1C],
    0x0F957: [0x7DBE],
    0x0F958: [0x83F1],
    0x0F959: [0x9675],
    0x0F95A: [0x8B80],
    0x0F95B: [0x62CF],
    0x0F95C: [0x6A02],
    0x0F95D: [0x8AFE],
    0x0F95E: [0x4E39],
    0x0F95F: [0x5BE7],
    0x0F960: [0x6012],
    0x0F961: [0x7387],
    0x0F962: [0x7570],
    0x0F963: [0x5317],
    0x0F964: [0x78FB],
    0x0F965: [0x4FBF],
    0x0F966: [0x5FA9],
    0x0F967: [0x4E0D],
    0x0F968: [0x6CCC],
    0x0F969: [0x6578],
    0x0F96A: [0x7D22],
    0x0F96B: [0x53C3],
    0x0F96C: [0x585E],
    0x0F96D: [0x7701],
    0x0F96E: [0x8449],
    0x0F96F: [0x8AAA],
    0x0F970: [0x6BBA],
    0x0F971: [0x8FB0],
    0x0F972: [0x6C88],
    0x0F973: [0x62FE],
    0x0F974: [0x82E5],
    0x0F975: [0x63A0],
    0x0F976: [0x7565],
    0x0F977: [0x4EAE],
    0x0F978: [0x5169],
    0x0F979: [0x51C9],
    0x0F97A: [0x6881],
    0x0F97B: [0x7CE7],
    0x0F97C: [0x826F],
    0x0F97D: [0x8AD2],
    0x0F97E: [0x91CF],
    0x0F97F: [0x52F5],
    0x0F980: [0x5442],
    0x0F981: [0x5973],
    0x0F982: [0x5EEC],
    0x0F983: [0x65C5],
    0x0F984: [0x6FFE],
    0x0F985: [0x792A],
    0x0F986: [0x95AD],
    0x0F987: [0x9A6A],
    0x0F988: [0x9E97],
    0x0F989: [0x9ECE],
    0x0F98A: [0x529B],
    0x0F98B: [0x66C6],
    0x0F98C: [0x6B77],
    0x0F98D: [0x8F62],
    0x0F98E: [0x5E74],
    0x0F98F: [0x6190],
    0x0F990: [0x6200],
    0x0F991: [0x649A],
    0x0F992: [0x6F23],
    0x0F993: [0x7149],
    0x0F994: [0x7489],
    0x0F995: [0x79CA],
    0x0F996: [0x7DF4],
    0x0F997: [0x806F],
    0x0F998: [0x8F26],
    0x0F999: [0x84EE],
    0x0F99A: [0x9023],
    0x0F99B: [0x934A],
    0x0F99C: [0x5217],
    0x0F99D: [0x52A3],
    0x0F99E: [0x54BD],
    0x0F99F: [0x70C8],
    0x0F9A0: [0x88C2],
    0x0F9A1: [0x8AAA],
    0x0F9A2: [0x5EC9],
    0x0F9A3: [0x5FF5],
    0x0F9A4: [0x637B],
    0x0F9A5: [0x6BAE],
    0x0F9A6: [0x7C3E],
    0x0F9A7: [0x7375],
    0x0F9A8: [0x4EE4],
    0x0F9A9: [0x56F9],
    0x0F9AA: [0x5BE7],
    0x0F9AB: [0x5DBA],
    0x0F9AC: [0x601C],
    0x0F9AD: [0x73B2],
    0x0F9AE: [0x7469],
    0x0F9AF: [0x7F9A],
    0x0F9B0: [0x8046],
    0x0F9B1: [0x9234],
    0x0F9B2: [0x96F6],
    0x0F9B3: [0x9748],
    0x0F9B4: [0x9818],
    0x0F9B5: [0x4F8B],
    0x0F9B6: [0x79AE],
    0x0F9B7: [0x91B4],
    0x0F9B8: [0x96B8],
    0x0F9B9: [0x60E1],
    0x0F9BA: [0x4E86],
    0x0F9BB: [0x50DA],
    0x0F9BC: [0x5BEE],
    0x0F9BD: [0x5C3F],
    0x0F9BE: [0x6599],
    0x0F9BF: [0x6A02],
    0x0F9C0: [0x71CE],
    0x0F9C1: [0x7642],
    0x0F9C2: [0x84FC],
    0x0F9C3: [0x907C],
    0x0F9C4: [0x9F8D],
    0x0F9C5: [0x6688],
    0x0F9C6: [0x962E],
    0x0F9C7: [0x5289],
    0x0F9C8: [0x677B],
    0x0F9C9: [0x67F3],
    0x0F9CA: [0x6D41],
    0x0F9CB: [0x6E9C],
    0x0F9CC: [0x7409],
    0x0F9CD: [0x7559],
    0x0F9CE: [0x786B],
    0x0F9CF: [0x7D10],
    0x0F9D0: [0x985E],
    0x0F9D1: [0x516D],
    0x0F9D2: [0x622E],
    0x0F9D3: [0x9678],
    0x0F9D4: [0x502B],
    0x0F9D5: [0x5D19],
    0x0F9D6: [0x6DEA],
    0x0F9D7: [0x8F2A],
    0x0F9D8: [0x5F8B],
    0x0F9D9: [0x6144],
    0x0F9DA: [0x6817],
    0x0F9DB: [0x7387],
    0x0F9DC: [0x9686],
    0x0F9DD: [0x5229],
    0x0F9DE: [0x540F],
    0x0F9DF: [0x5C65],
    0x0F9E0: [0x6613],
    0x0F9E1: [0x674E],
    0x0F9E2: [0x68A8],
    0x0F9E3: [0x6CE5],
    0x0F9E4: [0x7406],
    0x0F9E5: [0x75E2],
    0x0F9E6: [0x7F79],
    0x0F9E7: [0x88CF],
    0x0F9E8: [0x88E1],
    0x0F9E9: [0x91CC],
    0x0F9EA: [0x96E2],
    0x0F9EB: [0x533F],
    0x0F9EC: [0x6EBA],
    0x0F9ED: [0x541D],
    0x0F9EE: [0x71D0],
    0x0F9EF: [0x7498],
    0x0F9F0: [0x85FA],
    0x0F9F1: [0x96A3],
    0x0F9F2: [0x9C57],
    0x0F9F3: [0x9E9F],
    0x0F9F4: [0x6797],
    0x0F9F5: [0x6DCB],
    0x0F9F6: [0x81E8],
    0x0F9F7: [0x7ACB],
    0x0F9F8: [0x7B20],
    0x0F9F9: [0x7C92],
    0x0F9FA: [0x72C0],
    0x0F9FB: [0x7099],
    0x0F9FC: [0x8B58],
    0x0F9FD: [0x4EC0],
    0x0F9FE: [0x8336],
    0x0F9FF: [0x523A],
    0x0FA00: [0x5207],
    0x0FA01: [0x5EA6],
    0x0FA02: [0x62D3],
    0x0FA03: [0x7CD6],
    0x0FA04: [0x5B85],
    0x0FA05: [0x6D1E],
    0x0FA06: [0x66B4],
    0x0FA07: [0x8F3B],
    0x0FA08: [0x884C],
    0x0FA09: [0x964D],
    0x0FA0A: [0x898B],
    0x0FA0B: [0x5ED3],
    0x0FA0C: [0x5140],
    0x0FA0D: [0x55C0],
    0x0FA10: [0x585A],
    0x0FA12: [0x6674],
    0x0FA15: [0x51DE],
    0x0FA16: [0x732A],
    0x0FA17: [0x76CA],
    0x0FA18: [0x793C],
    0x0FA19: [0x795E],
    0x0FA1A: [0x7965],
    0x0FA1B: [0x798F],
    0x0FA1C: [0x9756],
    0x0FA1D: [0x7CBE],
    0x0FA1E: [0x7FBD],
    0x0FA20: [0x8612],
    0x0FA22: [0x8AF8],
    0x0FA25: [0x9038],
    0x0FA26: [0x90FD],
    0x0FA2A: [0x98EF],
    0x0FA2B: [0x98FC],
    0x0FA2C: [0x9928],
    0x0FA2D: [0x9DB4],
    0x0FA2E: [0x90DE],
    0x0FA2F: [0x96B7],
    0x0FA30: [0x4FAE],
    0x0FA31: [0x50E7],
    0x0FA32: [0x514D],
    0x0FA33: [0x52C9],
    0x0FA34: [0x52E4],
    0x0FA35: [0x5351],
    0x0FA36: [0x559D],
    0x0FA37: [0x5606],
    0x0FA38: [0x5668],
    0x0FA39: [0x5840],
    0x0FA3A: [0x58A8],
    0x0FA3B: [0x5C64],
    0x0FA3C: [0x5C6E],
    0x0FA3D: [0x6094],
    0x0FA3E: [0x6168],
    0x0FA3F: [0x618E],
    0x0FA40: [0x61F2],
    0x0FA41: [0x654F],
    0x0FA42: [0x65E2],
    0x0FA43: [0x6691],
    0x0FA44: [0x6885],
    0x0FA45: [0x6D77],
    0x0FA46: [0x6E1A],
    0x0FA47: [0x6F22],
    0x0FA48: [0x716E],
    0x0FA49: [0x722B],
    0x0FA4A: [0x7422],
    0x0FA4B: [0x7891],
    0x0FA4C: [0x793E],
    0x0FA4D: [0x7949],
    0x0FA4E: [0x7948],
    0x0FA4F: [0x7950],
    0x0FA50: [0x7956],
    0x0FA51: [0x795D],
    0x0FA52: [0x798D],
    0x0FA53: [0x798E],
    0x0FA54: [0x7A40],
    0x0FA55: [0x7A81],
    0x0FA56: [0x7BC0],
    0x0FA57: [0x7DF4],
    0x0FA58: [0x7E09],
    0x0FA59: [0x7E41],
    0x0FA5A: [0x7F72],
    0x0FA5B: [0x8005],
    0x0FA5C: [0x81ED],
    0x0FA5D: [0x8279],
    0x0FA5E: [0x8279],
    0x0FA5F: [0x8457],
    0x0FA60: [0x8910],
    0x0FA61: [0x8996],
    0x0FA62: [0x8B01],
    0x0FA63: [0x8B39],
    0x0FA64: [0x8CD3],
    0x0FA65: [0x8D08],
    0x0FA66: [0x8FB6],
    0x0FA67: [0x9038],
    0x0FA68: [0x96E3],
    0x0FA69: [0x97FF],
    0x0FA6A: [0x983B],
    0x0FA6B: [0x6075],
    0x0FA6C: [0x242EE],
    0x0FA6D: [0x8218],
    0x0FA70: [0x4E26],
    0x0FA71: [0x51B5],
    0x0FA72: [0x5168],
    0x0FA73: [0x4F80],
    0x0FA74: [0x5145],
    0x0FA75: [0x5180],
    0x0FA76: [0x52C7],
    0x0FA77: [0x52FA],
    0x0FA78: [0x559D],
    0x0FA79: [0x5555],
    0x0FA7A: [0x5599],
    0x0FA7B: [0x55E2],
    0x0FA7C: [0x585A],
    0x0FA7D: [0x58B3],
    0x0FA7E: [0x5944],
    0x0FA7F: [0x5954],
    0x0FA80: [0x5A62],
    0x0FA81: [0x5B28],
    0x0FA82: [0x5ED2],
    0x0FA83: [0x5ED9],
    0x0FA84: [0x5F69],
    0x0FA85: [0x5FAD],
    0x0FA86: [0x60D8],
    0x0FA87: [0x614E],
    0x0FA88: [0x6108],
    0x0FA89: [0x618E],
    0x0FA8A: [0x6160],
    0x0FA8B: [0x61F2],
    0x0FA8C: [0x6234],
    0x0FA8D: [0x63C4],
    0x0FA8E: [0x641C],
    0x0FA8F: [0x6452],
    0x0FA90: [0x6556],
    0x0FA91: [0x6674],
    0x0FA92: [0x6717],
    0x0FA93: [0x671B],
    0x0FA94: [0x6756],
    0x0FA95: [0x6B79],
    0x0FA96: [0x6BBA],
    0x0FA97: [0x6D41],
    0x0FA98: [0x6EDB],
    0x0FA99: [0x6ECB],
    0x0FA9A: [0x6F22],
    0x0FA9B: [0x701E],
    0x0FA9C: [0x716E],
    0x0FA9D: [0x77A7],
    0x0FA9E: [0x7235],
    0x0FA9F: [0x72AF],
    0x0FAA0: [0x732A],
    0x0FAA1: [0x7471],
    0x0FAA2: [0x7506],
    0x0FAA3: [0x753B],
    0x0FAA4: [0x761D],
    0x0FAA5: [0x761F],
    0x0FAA6: [0x76CA],
    0x0FAA7: [0x76DB],
    0x0FAA8: [0x76F4],
    0x0FAA9: [0x774A],
    0x0FAAA: [0x7740],
    0x0FAAB: [0x78CC],
    0x0FAAC: [0x7AB1],
    0x0FAAD: [0x7BC0],
    0x0FAAE: [0x7C7B],
    0x0FAAF: [0x7D5B],
    0x0FAB0: [0x7DF4],
    0x0FAB1: [0x7F3E],
    0x0FAB2: [0x8005],
    0x0FAB3: [0x8352],
    0x0FAB4: [0x83EF],
    0x0FAB5: [0x8779],
    0x0FAB6: [0x8941],
    0x0FAB7: [0x8986],
    0x0FAB8: [0x8996],
    0x0FAB9: [0x8ABF],
    0x0FABA: [0x8AF8],
    0x0FABB: [0x8ACB],
    0x0FABC: [0x8B01],
    0x0FABD: [0x8AFE],
    0x0FABE: [0x8AED],
    0x0FABF: [0x8B39],
    0x0FAC0: [0x8B8A],
    0x0FAC1: [0x8D08],
    0x0FAC2: [0x8F38],
    0x0FAC3: [0x9072],
    0x0FAC4: [0x9199],
    0x0FAC5: [0x9276],
    0x0FAC6: [0x967C],
    0x0FAC7: [0x96E3],
    0x0FAC8: [0x9756],
    0x0FAC9: [0x97DB],
    0x0FACA: [0x97FF],
    0x0FACB: [0x980B],
    0x0FACC: [0x983B],
    0x0FACD: [0x9B12],
    0x0FACE: [0x9F9C],
    0x0FACF: [0x2284A],
    0x0FAD0: [0x22844],
    0x0FAD1: [0x233D5],
    0x0FAD2: [0x3B9D],
    0x0FAD3: [0x4018],
    0x0FAD4: [0x4039],
    0x0FAD5: [0x25249],
    0x0FAD6: [0x25CD0],
    0x0FAD7: [0x27ED3],
    0x0FAD8: [0x9F43],
    0x0FAD9: [0x9F8E],
    0x0FB00: ["<compat>", 0x0066, 0x0066],
    0x0FB01: ["<compat>", 0x0066, 0x0069],
    0x0FB02: ["<compat>", 0x0066, 0x006C],
    0x0FB03: ["<compat>", 0x0066, 0x0066, 0x0069],
    0x0FB04: ["<compat>", 0x0066, 0x0066, 0x006C],
    0x0FB05: ["<compat>", 0x017F, 0x0074],
    0x0FB06: ["<compat>", 0x0073, 0x0074],
    0x0FB13: ["<compat>", 0x0574, 0x0576],
    0x0FB14: ["<compat>", 0x0574, 0x0565],
    0x0FB15: ["<compat>", 0x0574, 0x056B],
    0x0FB16: ["<compat>", 0x057E, 0x0576],
    0x0FB17: ["<compat>", 0x0574, 0x056D],
    0x0FB1D: [0x05D9, 0x05B4],
    0x0FB1F: [0x05F2, 0x05B7],
    0x0FB20: ["<font>", 0x05E2],
    0x0FB21: ["<font>", 0x05D0],
    0x0FB22: ["<font>", 0x05D3],
    0x0FB23: ["<font>", 0x05D4],
    0x0FB24: ["<font>", 0x05DB],
    0x0FB25: ["<font>", 0x05DC],
    0x0FB26: ["<font>", 0x05DD],
    0x0FB27: ["<font>", 0x05E8],
    0x0FB28: ["<font>", 0x05EA],
    0x0FB29: ["<font>", 0x002B],
    0x0FB2A: [0x05E9, 0x05C1],
    0x0FB2B: [0x05E9, 0x05C2],
    0x0FB2C: [0xFB49, 0x05C1],
    0x0FB2D: [0xFB49, 0x05C2],
    0x0FB2E: [0x05D0, 0x05B7],
    0x0FB2F: [0x05D0, 0x05B8],
    0x0FB30: [0x05D0, 0x05BC],
    0x0FB31: [0x05D1, 0x05BC],
    0x0FB32: [0x05D2, 0x05BC],
    0x0FB33: [0x05D3, 0x05BC],
    0x0FB34: [0x05D4, 0x05BC],
    0x0FB35: [0x05D5, 0x05BC],
    0x0FB36: [0x05D6, 0x05BC],
    0x0FB38: [0x05D8, 0x05BC],
    0x0FB39: [0x05D9, 0x05BC],
    0x0FB3A: [0x05DA, 0x05BC],
    0x0FB3B: [0x05DB, 0x05BC],
    0x0FB3C: [0x05DC, 0x05BC],
    0x0FB3E: [0x05DE, 0x05BC],
    0x0FB40: [0x05E0, 0x05BC],
    0x0FB41: [0x05E1, 0x05BC],
    0x0FB43: [0x05E3, 0x05BC],
    0x0FB44: [0x05E4, 0x05BC],
    0x0FB46: [0x05E6, 0x05BC],
    0x0FB47: [0x05E7, 0x05BC],
    0x0FB48: [0x05E8, 0x05BC],
    0x0FB49: [0x05E9, 0x05BC],
    0x0FB4A: [0x05EA, 0x05BC],
    0x0FB4B: [0x05D5, 0x05B9],
    0x0FB4C: [0x05D1, 0x05BF],
    0x0FB4D: [0x05DB, 0x05BF],
    0x0FB4E: [0x05E4, 0x05BF],
    0x0FB4F: ["<compat>", 0x05D0, 0x05DC],
    0x0FB50: ["<isolated>", 0x0671],
    0x0FB51: ["<final>", 0x0671],
    0x0FB52: ["<isolated>", 0x067B],
    0x0FB53: ["<final>", 0x067B],
    0x0FB54: ["<initial>", 0x067B],
    0x0FB55: ["<medial>", 0x067B],
    0x0FB56: ["<isolated>", 0x067E],
    0x0FB57: ["<final>", 0x067E],
    0x0FB58: ["<initial>", 0x067E],
    0x0FB59: ["<medial>", 0x067E],
    0x0FB5A: ["<isolated>", 0x0680],
    0x0FB5B: ["<final>", 0x0680],
    0x0FB5C: ["<initial>", 0x0680],
    0x0FB5D: ["<medial>", 0x0680],
    0x0FB5E: ["<isolated>", 0x067A],
    0x0FB5F: ["<final>", 0x067A],
    0x0FB60: ["<initial>", 0x067A],
    0x0FB61: ["<medial>", 0x067A],
    0x0FB62: ["<isolated>", 0x067F],
    0x0FB63: ["<final>", 0x067F],
    0x0FB64: ["<initial>", 0x067F],
    0x0FB65: ["<medial>", 0x067F],
    0x0FB66: ["<isolated>", 0x0679],
    0x0FB67: ["<final>", 0x0679],
    0x0FB68: ["<initial>", 0x0679],
    0x0FB69: ["<medial>", 0x0679],
    0x0FB6A: ["<isolated>", 0x06A4],
    0x0FB6B: ["<final>", 0x06A4],
    0x0FB6C: ["<initial>", 0x06A4],
    0x0FB6D: ["<medial>", 0x06A4],
    0x0FB6E: ["<isolated>", 0x06A6],
    0x0FB6F: ["<final>", 0x06A6],
    0x0FB70: ["<initial>", 0x06A6],
    0x0FB71: ["<medial>", 0x06A6],
    0x0FB72: ["<isolated>", 0x0684],
    0x0FB73: ["<final>", 0x0684],
    0x0FB74: ["<initial>", 0x0684],
    0x0FB75: ["<medial>", 0x0684],
    0x0FB76: ["<isolated>", 0x0683],
    0x0FB77: ["<final>", 0x0683],
    0x0FB78: ["<initial>", 0x0683],
    0x0FB79: ["<medial>", 0x0683],
    0x0FB7A: ["<isolated>", 0x0686],
    0x0FB7B: ["<final>", 0x0686],
    0x0FB7C: ["<initial>", 0x0686],
    0x0FB7D: ["<medial>", 0x0686],
    0x0FB7E: ["<isolated>", 0x0687],
    0x0FB7F: ["<final>", 0x0687],
    0x0FB80: ["<initial>", 0x0687],
    0x0FB81: ["<medial>", 0x0687],
    0x0FB82: ["<isolated>", 0x068D],
    0x0FB83: ["<final>", 0x068D],
    0x0FB84: ["<isolated>", 0x068C],
    0x0FB85: ["<final>", 0x068C],
    0x0FB86: ["<isolated>", 0x068E],
    0x0FB87: ["<final>", 0x068E],
    0x0FB88: ["<isolated>", 0x0688],
    0x0FB89: ["<final>", 0x0688],
    0x0FB8A: ["<isolated>", 0x0698],
    0x0FB8B: ["<final>", 0x0698],
    0x0FB8C: ["<isolated>", 0x0691],
    0x0FB8D: ["<final>", 0x0691],
    0x0FB8E: ["<isolated>", 0x06A9],
    0x0FB8F: ["<final>", 0x06A9],
    0x0FB90: ["<initial>", 0x06A9],
    0x0FB91: ["<medial>", 0x06A9],
    0x0FB92: ["<isolated>", 0x06AF],
    0x0FB93: ["<final>", 0x06AF],
    0x0FB94: ["<initial>", 0x06AF],
    0x0FB95: ["<medial>", 0x06AF],
    0x0FB96: ["<isolated>", 0x06B3],
    0x0FB97: ["<final>", 0x06B3],
    0x0FB98: ["<initial>", 0x06B3],
    0x0FB99: ["<medial>", 0x06B3],
    0x0FB9A: ["<isolated>", 0x06B1],
    0x0FB9B: ["<final>", 0x06B1],
    0x0FB9C: ["<initial>", 0x06B1],
    0x0FB9D: ["<medial>", 0x06B1],
    0x0FB9E: ["<isolated>", 0x06BA],
    0x0FB9F: ["<final>", 0x06BA],
    0x0FBA0: ["<isolated>", 0x06BB],
    0x0FBA1: ["<final>", 0x06BB],
    0x0FBA2: ["<initial>", 0x06BB],
    0x0FBA3: ["<medial>", 0x06BB],
    0x0FBA4: ["<isolated>", 0x06C0],
    0x0FBA5: ["<final>", 0x06C0],
    0x0FBA6: ["<isolated>", 0x06C1],
    0x0FBA7: ["<final>", 0x06C1],
    0x0FBA8: ["<initial>", 0x06C1],
    0x0FBA9: ["<medial>", 0x06C1],
    0x0FBAA: ["<isolated>", 0x06BE],
    0x0FBAB: ["<final>", 0x06BE],
    0x0FBAC: ["<initial>", 0x06BE],
    0x0FBAD: ["<medial>", 0x06BE],
    0x0FBAE: ["<isolated>", 0x06D2],
    0x0FBAF: ["<final>", 0x06D2],
    0x0FBB0: ["<isolated>", 0x06D3],
    0x0FBB1: ["<final>", 0x06D3],
    0x0FBD3: ["<isolated>", 0x06AD],
    0x0FBD4: ["<final>", 0x06AD],
    0x0FBD5: ["<initial>", 0x06AD],
    0x0FBD6: ["<medial>", 0x06AD],
    0x0FBD7: ["<isolated>", 0x06C7],
    0x0FBD8: ["<final>", 0x06C7],
    0x0FBD9: ["<isolated>", 0x06C6],
    0x0FBDA: ["<final>", 0x06C6],
    0x0FBDB: ["<isolated>", 0x06C8],
    0x0FBDC: ["<final>", 0x06C8],
    0x0FBDD: ["<isolated>", 0x0677],
    0x0FBDE: ["<isolated>", 0x06CB],
    0x0FBDF: ["<final>", 0x06CB],
    0x0FBE0: ["<isolated>", 0x06C5],
    0x0FBE1: ["<final>", 0x06C5],
    0x0FBE2: ["<isolated>", 0x06C9],
    0x0FBE3: ["<final>", 0x06C9],
    0x0FBE4: ["<isolated>", 0x06D0],
    0x0FBE5: ["<final>", 0x06D0],
    0x0FBE6: ["<initial>", 0x06D0],
    0x0FBE7: ["<medial>", 0x06D0],
    0x0FBE8: ["<initial>", 0x0649],
    0x0FBE9: ["<medial>", 0x0649],
    0x0FBEA: ["<isolated>", 0x0626, 0x0627],
    0x0FBEB: ["<final>", 0x0626, 0x0627],
    0x0FBEC: ["<isolated>", 0x0626, 0x06D5],
    0x0FBED: ["<final>", 0x0626, 0x06D5],
    0x0FBEE: ["<isolated>", 0x0626, 0x0648],
    0x0FBEF: ["<final>", 0x0626, 0x0648],
    0x0FBF0: ["<isolated>", 0x0626, 0x06C7],
    0x0FBF1: ["<final>", 0x0626, 0x06C7],
    0x0FBF2: ["<isolated>", 0x0626, 0x06C6],
    0x0FBF3: ["<final>", 0x0626, 0x06C6],
    0x0FBF4: ["<isolated>", 0x0626, 0x06C8],
    0x0FBF5: ["<final>", 0x0626, 0x06C8],
    0x0FBF6: ["<isolated>", 0x0626, 0x06D0],
    0x0FBF7: ["<final>", 0x0626, 0x06D0],
    0x0FBF8: ["<initial>", 0x0626, 0x06D0],
    0x0FBF9: ["<isolated>", 0x0626, 0x0649],
    0x0FBFA: ["<final>", 0x0626, 0x0649],
    0x0FBFB: ["<initial>", 0x0626, 0x0649],
    0x0FBFC: ["<isolated>", 0x06CC],
    0x0FBFD: ["<final>", 0x06CC],
    0x0FBFE: ["<initial>", 0x06CC],
    0x0FBFF: ["<medial>", 0x06CC],
    0x0FC00: ["<isolated>", 0x0626, 0x062C],
    0x0FC01: ["<isolated>", 0x0626, 0x062D],
    0x0FC02: ["<isolated>", 0x0626, 0x0645],
    0x0FC03: ["<isolated>", 0x0626, 0x0649],
    0x0FC04: ["<isolated>", 0x0626, 0x064A],
    0x0FC05: ["<isolated>", 0x0628, 0x062C],
    0x0FC06: ["<isolated>", 0x0628, 0x062D],
    0x0FC07: ["<isolated>", 0x0628, 0x062E],
    0x0FC08: ["<isolated>", 0x0628, 0x0645],
    0x0FC09: ["<isolated>", 0x0628, 0x0649],
    0x0FC0A: ["<isolated>", 0x0628, 0x064A],
    0x0FC0B: ["<isolated>", 0x062A, 0x062C],
    0x0FC0C: ["<isolated>", 0x062A, 0x062D],
    0x0FC0D: ["<isolated>", 0x062A, 0x062E],
    0x0FC0E: ["<isolated>", 0x062A, 0x0645],
    0x0FC0F: ["<isolated>", 0x062A, 0x0649],
    0x0FC10: ["<isolated>", 0x062A, 0x064A],
    0x0FC11: ["<isolated>", 0x062B, 0x062C],
    0x0FC12: ["<isolated>", 0x062B, 0x0645],
    0x0FC13: ["<isolated>", 0x062B, 0x0649],
    0x0FC14: ["<isolated>", 0x062B, 0x064A],
    0x0FC15: ["<isolated>", 0x062C, 0x062D],
    0x0FC16: ["<isolated>", 0x062C, 0x0645],
    0x0FC17: ["<isolated>", 0x062D, 0x062C],
    0x0FC18: ["<isolated>", 0x062D, 0x0645],
    0x0FC19: ["<isolated>", 0x062E, 0x062C],
    0x0FC1A: ["<isolated>", 0x062E, 0x062D],
    0x0FC1B: ["<isolated>", 0x062E, 0x0645],
    0x0FC1C: ["<isolated>", 0x0633, 0x062C],
    0x0FC1D: ["<isolated>", 0x0633, 0x062D],
    0x0FC1E: ["<isolated>", 0x0633, 0x062E],
    0x0FC1F: ["<isolated>", 0x0633, 0x0645],
    0x0FC20: ["<isolated>", 0x0635, 0x062D],
    0x0FC21: ["<isolated>", 0x0635, 0x0645],
    0x0FC22: ["<isolated>", 0x0636, 0x062C],
    0x0FC23: ["<isolated>", 0x0636, 0x062D],
    0x0FC24: ["<isolated>", 0x0636, 0x062E],
    0x0FC25: ["<isolated>", 0x0636, 0x0645],
    0x0FC26: ["<isolated>", 0x0637, 0x062D],
    0x0FC27: ["<isolated>", 0x0637, 0x0645],
    0x0FC28: ["<isolated>", 0x0638, 0x0645],
    0x0FC29: ["<isolated>", 0x0639, 0x062C],
    0x0FC2A: ["<isolated>", 0x0639, 0x0645],
    0x0FC2B: ["<isolated>", 0x063A, 0x062C],
    0x0FC2C: ["<isolated>", 0x063A, 0x0645],
    0x0FC2D: ["<isolated>", 0x0641, 0x062C],
    0x0FC2E: ["<isolated>", 0x0641, 0x062D],
    0x0FC2F: ["<isolated>", 0x0641, 0x062E],
    0x0FC30: ["<isolated>", 0x0641, 0x0645],
    0x0FC31: ["<isolated>", 0x0641, 0x0649],
    0x0FC32: ["<isolated>", 0x0641, 0x064A],
    0x0FC33: ["<isolated>", 0x0642, 0x062D],
    0x0FC34: ["<isolated>", 0x0642, 0x0645],
    0x0FC35: ["<isolated>", 0x0642, 0x0649],
    0x0FC36: ["<isolated>", 0x0642, 0x064A],
    0x0FC37: ["<isolated>", 0x0643, 0x0627],
    0x0FC38: ["<isolated>", 0x0643, 0x062C],
    0x0FC39: ["<isolated>", 0x0643, 0x062D],
    0x0FC3A: ["<isolated>", 0x0643, 0x062E],
    0x0FC3B: ["<isolated>", 0x0643, 0x0644],
    0x0FC3C: ["<isolated>", 0x0643, 0x0645],
    0x0FC3D: ["<isolated>", 0x0643, 0x0649],
    0x0FC3E: ["<isolated>", 0x0643, 0x064A],
    0x0FC3F: ["<isolated>", 0x0644, 0x062C],
    0x0FC40: ["<isolated>", 0x0644, 0x062D],
    0x0FC41: ["<isolated>", 0x0644, 0x062E],
    0x0FC42: ["<isolated>", 0x0644, 0x0645],
    0x0FC43: ["<isolated>", 0x0644, 0x0649],
    0x0FC44: ["<isolated>", 0x0644, 0x064A],
    0x0FC45: ["<isolated>", 0x0645, 0x062C],
    0x0FC46: ["<isolated>", 0x0645, 0x062D],
    0x0FC47: ["<isolated>", 0x0645, 0x062E],
    0x0FC48: ["<isolated>", 0x0645, 0x0645],
    0x0FC49: ["<isolated>", 0x0645, 0x0649],
    0x0FC4A: ["<isolated>", 0x0645, 0x064A],
    0x0FC4B: ["<isolated>", 0x0646, 0x062C],
    0x0FC4C: ["<isolated>", 0x0646, 0x062D],
    0x0FC4D: ["<isolated>", 0x0646, 0x062E],
    0x0FC4E: ["<isolated>", 0x0646, 0x0645],
    0x0FC4F: ["<isolated>", 0x0646, 0x0649],
    0x0FC50: ["<isolated>", 0x0646, 0x064A],
    0x0FC51: ["<isolated>", 0x0647, 0x062C],
    0x0FC52: ["<isolated>", 0x0647, 0x0645],
    0x0FC53: ["<isolated>", 0x0647, 0x0649],
    0x0FC54: ["<isolated>", 0x0647, 0x064A],
    0x0FC55: ["<isolated>", 0x064A, 0x062C],
    0x0FC56: ["<isolated>", 0x064A, 0x062D],
    0x0FC57: ["<isolated>", 0x064A, 0x062E],
    0x0FC58: ["<isolated>", 0x064A, 0x0645],
    0x0FC59: ["<isolated>", 0x064A, 0x0649],
    0x0FC5A: ["<isolated>", 0x064A, 0x064A],
    0x0FC5B: ["<isolated>", 0x0630, 0x0670],
    0x0FC5C: ["<isolated>", 0x0631, 0x0670],
    0x0FC5D: ["<isolated>", 0x0649, 0x0670],
    0x0FC5E: ["<isolated>", 0x0020, 0x064C, 0x0651],
    0x0FC5F: ["<isolated>", 0x0020, 0x064D, 0x0651],
    0x0FC60: ["<isolated>", 0x0020, 0x064E, 0x0651],
    0x0FC61: ["<isolated>", 0x0020, 0x064F, 0x0651],
    0x0FC62: ["<isolated>", 0x0020, 0x0650, 0x0651],
    0x0FC63: ["<isolated>", 0x0020, 0x0651, 0x0670],
    0x0FC64: ["<final>", 0x0626, 0x0631],
    0x0FC65: ["<final>", 0x0626, 0x0632],
    0x0FC66: ["<final>", 0x0626, 0x0645],
    0x0FC67: ["<final>", 0x0626, 0x0646],
    0x0FC68: ["<final>", 0x0626, 0x0649],
    0x0FC69: ["<final>", 0x0626, 0x064A],
    0x0FC6A: ["<final>", 0x0628, 0x0631],
    0x0FC6B: ["<final>", 0x0628, 0x0632],
    0x0FC6C: ["<final>", 0x0628, 0x0645],
    0x0FC6D: ["<final>", 0x0628, 0x0646],
    0x0FC6E: ["<final>", 0x0628, 0x0649],
    0x0FC6F: ["<final>", 0x0628, 0x064A],
    0x0FC70: ["<final>", 0x062A, 0x0631],
    0x0FC71: ["<final>", 0x062A, 0x0632],
    0x0FC72: ["<final>", 0x062A, 0x0645],
    0x0FC73: ["<final>", 0x062A, 0x0646],
    0x0FC74: ["<final>", 0x062A, 0x0649],
    0x0FC75: ["<final>", 0x062A, 0x064A],
    0x0FC76: ["<final>", 0x062B, 0x0631],
    0x0FC77: ["<final>", 0x062B, 0x0632],
    0x0FC78: ["<final>", 0x062B, 0x0645],
    0x0FC79: ["<final>", 0x062B, 0x0646],
    0x0FC7A: ["<final>", 0x062B, 0x0649],
    0x0FC7B: ["<final>", 0x062B, 0x064A],
    0x0FC7C: ["<final>", 0x0641, 0x0649],
    0x0FC7D: ["<final>", 0x0641, 0x064A],
    0x0FC7E: ["<final>", 0x0642, 0x0649],
    0x0FC7F: ["<final>", 0x0642, 0x064A],
    0x0FC80: ["<final>", 0x0643, 0x0627],
    0x0FC81: ["<final>", 0x0643, 0x0644],
    0x0FC82: ["<final>", 0x0643, 0x0645],
    0x0FC83: ["<final>", 0x0643, 0x0649],
    0x0FC84: ["<final>", 0x0643, 0x064A],
    0x0FC85: ["<final>", 0x0644, 0x0645],
    0x0FC86: ["<final>", 0x0644, 0x0649],
    0x0FC87: ["<final>", 0x0644, 0x064A],
    0x0FC88: ["<final>", 0x0645, 0x0627],
    0x0FC89: ["<final>", 0x0645, 0x0645],
    0x0FC8A: ["<final>", 0x0646, 0x0631],
    0x0FC8B: ["<final>", 0x0646, 0x0632],
    0x0FC8C: ["<final>", 0x0646, 0x0645],
    0x0FC8D: ["<final>", 0x0646, 0x0646],
    0x0FC8E: ["<final>", 0x0646, 0x0649],
    0x0FC8F: ["<final>", 0x0646, 0x064A],
    0x0FC90: ["<final>", 0x0649, 0x0670],
    0x0FC91: ["<final>", 0x064A, 0x0631],
    0x0FC92: ["<final>", 0x064A, 0x0632],
    0x0FC93: ["<final>", 0x064A, 0x0645],
    0x0FC94: ["<final>", 0x064A, 0x0646],
    0x0FC95: ["<final>", 0x064A, 0x0649],
    0x0FC96: ["<final>", 0x064A, 0x064A],
    0x0FC97: ["<initial>", 0x0626, 0x062C],
    0x0FC98: ["<initial>", 0x0626, 0x062D],
    0x0FC99: ["<initial>", 0x0626, 0x062E],
    0x0FC9A: ["<initial>", 0x0626, 0x0645],
    0x0FC9B: ["<initial>", 0x0626, 0x0647],
    0x0FC9C: ["<initial>", 0x0628, 0x062C],
    0x0FC9D: ["<initial>", 0x0628, 0x062D],
    0x0FC9E: ["<initial>", 0x0628, 0x062E],
    0x0FC9F: ["<initial>", 0x0628, 0x0645],
    0x0FCA0: ["<initial>", 0x0628, 0x0647],
    0x0FCA1: ["<initial>", 0x062A, 0x062C],
    0x0FCA2: ["<initial>", 0x062A, 0x062D],
    0x0FCA3: ["<initial>", 0x062A, 0x062E],
    0x0FCA4: ["<initial>", 0x062A, 0x0645],
    0x0FCA5: ["<initial>", 0x062A, 0x0647],
    0x0FCA6: ["<initial>", 0x062B, 0x0645],
    0x0FCA7: ["<initial>", 0x062C, 0x062D],
    0x0FCA8: ["<initial>", 0x062C, 0x0645],
    0x0FCA9: ["<initial>", 0x062D, 0x062C],
    0x0FCAA: ["<initial>", 0x062D, 0x0645],
    0x0FCAB: ["<initial>", 0x062E, 0x062C],
    0x0FCAC: ["<initial>", 0x062E, 0x0645],
    0x0FCAD: ["<initial>", 0x0633, 0x062C],
    0x0FCAE: ["<initial>", 0x0633, 0x062D],
    0x0FCAF: ["<initial>", 0x0633, 0x062E],
    0x0FCB0: ["<initial>", 0x0633, 0x0645],
    0x0FCB1: ["<initial>", 0x0635, 0x062D],
    0x0FCB2: ["<initial>", 0x0635, 0x062E],
    0x0FCB3: ["<initial>", 0x0635, 0x0645],
    0x0FCB4: ["<initial>", 0x0636, 0x062C],
    0x0FCB5: ["<initial>", 0x0636, 0x062D],
    0x0FCB6: ["<initial>", 0x0636, 0x062E],
    0x0FCB7: ["<initial>", 0x0636, 0x0645],
    0x0FCB8: ["<initial>", 0x0637, 0x062D],
    0x0FCB9: ["<initial>", 0x0638, 0x0645],
    0x0FCBA: ["<initial>", 0x0639, 0x062C],
    0x0FCBB: ["<initial>", 0x0639, 0x0645],
    0x0FCBC: ["<initial>", 0x063A, 0x062C],
    0x0FCBD: ["<initial>", 0x063A, 0x0645],
    0x0FCBE: ["<initial>", 0x0641, 0x062C],
    0x0FCBF: ["<initial>", 0x0641, 0x062D],
    0x0FCC0: ["<initial>", 0x0641, 0x062E],
    0x0FCC1: ["<initial>", 0x0641, 0x0645],
    0x0FCC2: ["<initial>", 0x0642, 0x062D],
    0x0FCC3: ["<initial>", 0x0642, 0x0645],
    0x0FCC4: ["<initial>", 0x0643, 0x062C],
    0x0FCC5: ["<initial>", 0x0643, 0x062D],
    0x0FCC6: ["<initial>", 0x0643, 0x062E],
    0x0FCC7: ["<initial>", 0x0643, 0x0644],
    0x0FCC8: ["<initial>", 0x0643, 0x0645],
    0x0FCC9: ["<initial>", 0x0644, 0x062C],
    0x0FCCA: ["<initial>", 0x0644, 0x062D],
    0x0FCCB: ["<initial>", 0x0644, 0x062E],
    0x0FCCC: ["<initial>", 0x0644, 0x0645],
    0x0FCCD: ["<initial>", 0x0644, 0x0647],
    0x0FCCE: ["<initial>", 0x0645, 0x062C],
    0x0FCCF: ["<initial>", 0x0645, 0x062D],
    0x0FCD0: ["<initial>", 0x0645, 0x062E],
    0x0FCD1: ["<initial>", 0x0645, 0x0645],
    0x0FCD2: ["<initial>", 0x0646, 0x062C],
    0x0FCD3: ["<initial>", 0x0646, 0x062D],
    0x0FCD4: ["<initial>", 0x0646, 0x062E],
    0x0FCD5: ["<initial>", 0x0646, 0x0645],
    0x0FCD6: ["<initial>", 0x0646, 0x0647],
    0x0FCD7: ["<initial>", 0x0647, 0x062C],
    0x0FCD8: ["<initial>", 0x0647, 0x0645],
    0x0FCD9: ["<initial>", 0x0647, 0x0670],
    0x0FCDA: ["<initial>", 0x064A, 0x062C],
    0x0FCDB: ["<initial>", 0x064A, 0x062D],
    0x0FCDC: ["<initial>", 0x064A, 0x062E],
    0x0FCDD: ["<initial>", 0x064A, 0x0645],
    0x0FCDE: ["<initial>", 0x064A, 0x0647],
    0x0FCDF: ["<medial>", 0x0626, 0x0645],
    0x0FCE0: ["<medial>", 0x0626, 0x0647],
    0x0FCE1: ["<medial>", 0x0628, 0x0645],
    0x0FCE2: ["<medial>", 0x0628, 0x0647],
    0x0FCE3: ["<medial>", 0x062A, 0x0645],
    0x0FCE4: ["<medial>", 0x062A, 0x0647],
    0x0FCE5: ["<medial>", 0x062B, 0x0645],
    0x0FCE6: ["<medial>", 0x062B, 0x0647],
    0x0FCE7: ["<medial>", 0x0633, 0x0645],
    0x0FCE8: ["<medial>", 0x0633, 0x0647],
    0x0FCE9: ["<medial>", 0x0634, 0x0645],
    0x0FCEA: ["<medial>", 0x0634, 0x0647],
    0x0FCEB: ["<medial>", 0x0643, 0x0644],
    0x0FCEC: ["<medial>", 0x0643, 0x0645],
    0x0FCED: ["<medial>", 0x0644, 0x0645],
    0x0FCEE: ["<medial>", 0x0646, 0x0645],
    0x0FCEF: ["<medial>", 0x0646, 0x0647],
    0x0FCF0: ["<medial>", 0x064A, 0x0645],
    0x0FCF1: ["<medial>", 0x064A, 0x0647],
    0x0FCF2: ["<medial>", 0x0640, 0x064E, 0x0651],
    0x0FCF3: ["<medial>", 0x0640, 0x064F, 0x0651],
    0x0FCF4: ["<medial>", 0x0640, 0x0650, 0x0651],
    0x0FCF5: ["<isolated>", 0x0637, 0x0649],
    0x0FCF6: ["<isolated>", 0x0637, 0x064A],
    0x0FCF7: ["<isolated>", 0x0639, 0x0649],
    0x0FCF8: ["<isolated>", 0x0639, 0x064A],
    0x0FCF9: ["<isolated>", 0x063A, 0x0649],
    0x0FCFA: ["<isolated>", 0x063A, 0x064A],
    0x0FCFB: ["<isolated>", 0x0633, 0x0649],
    0x0FCFC: ["<isolated>", 0x0633, 0x064A],
    0x0FCFD: ["<isolated>", 0x0634, 0x0649],
    0x0FCFE: ["<isolated>", 0x0634, 0x064A],
    0x0FCFF: ["<isolated>", 0x062D, 0x0649],
    0x0FD00: ["<isolated>", 0x062D, 0x064A],
    0x0FD01: ["<isolated>", 0x062C, 0x0649],
    0x0FD02: ["<isolated>", 0x062C, 0x064A],
    0x0FD03: ["<isolated>", 0x062E, 0x0649],
    0x0FD04: ["<isolated>", 0x062E, 0x064A],
    0x0FD05: ["<isolated>", 0x0635, 0x0649],
    0x0FD06: ["<isolated>", 0x0635, 0x064A],
    0x0FD07: ["<isolated>", 0x0636, 0x0649],
    0x0FD08: ["<isolated>", 0x0636, 0x064A],
    0x0FD09: ["<isolated>", 0x0634, 0x062C],
    0x0FD0A: ["<isolated>", 0x0634, 0x062D],
    0x0FD0B: ["<isolated>", 0x0634, 0x062E],
    0x0FD0C: ["<isolated>", 0x0634, 0x0645],
    0x0FD0D: ["<isolated>", 0x0634, 0x0631],
    0x0FD0E: ["<isolated>", 0x0633, 0x0631],
    0x0FD0F: ["<isolated>", 0x0635, 0x0631],
    0x0FD10: ["<isolated>", 0x0636, 0x0631],
    0x0FD11: ["<final>", 0x0637, 0x0649],
    0x0FD12: ["<final>", 0x0637, 0x064A],
    0x0FD13: ["<final>", 0x0639, 0x0649],
    0x0FD14: ["<final>", 0x0639, 0x064A],
    0x0FD15: ["<final>", 0x063A, 0x0649],
    0x0FD16: ["<final>", 0x063A, 0x064A],
    0x0FD17: ["<final>", 0x0633, 0x0649],
    0x0FD18: ["<final>", 0x0633, 0x064A],
    0x0FD19: ["<final>", 0x0634, 0x0649],
    0x0FD1A: ["<final>", 0x0634, 0x064A],
    0x0FD1B: ["<final>", 0x062D, 0x0649],
    0x0FD1C: ["<final>", 0x062D, 0x064A],
    0x0FD1D: ["<final>", 0x062C, 0x0649],
    0x0FD1E: ["<final>", 0x062C, 0x064A],
    0x0FD1F: ["<final>", 0x062E, 0x0649],
    0x0FD20: ["<final>", 0x062E, 0x064A],
    0x0FD21: ["<final>", 0x0635, 0x0649],
    0x0FD22: ["<final>", 0x0635, 0x064A],
    0x0FD23: ["<final>", 0x0636, 0x0649],
    0x0FD24: ["<final>", 0x0636, 0x064A],
    0x0FD25: ["<final>", 0x0634, 0x062C],
    0x0FD26: ["<final>", 0x0634, 0x062D],
    0x0FD27: ["<final>", 0x0634, 0x062E],
    0x0FD28: ["<final>", 0x0634, 0x0645],
    0x0FD29: ["<final>", 0x0634, 0x0631],
    0x0FD2A: ["<final>", 0x0633, 0x0631],
    0x0FD2B: ["<final>", 0x0635, 0x0631],
    0x0FD2C: ["<final>", 0x0636, 0x0631],
    0x0FD2D: ["<initial>", 0x0634, 0x062C],
    0x0FD2E: ["<initial>", 0x0634, 0x062D],
    0x0FD2F: ["<initial>", 0x0634, 0x062E],
    0x0FD30: ["<initial>", 0x0634, 0x0645],
    0x0FD31: ["<initial>", 0x0633, 0x0647],
    0x0FD32: ["<initial>", 0x0634, 0x0647],
    0x0FD33: ["<initial>", 0x0637, 0x0645],
    0x0FD34: ["<medial>", 0x0633, 0x062C],
    0x0FD35: ["<medial>", 0x0633, 0x062D],
    0x0FD36: ["<medial>", 0x0633, 0x062E],
    0x0FD37: ["<medial>", 0x0634, 0x062C],
    0x0FD38: ["<medial>", 0x0634, 0x062D],
    0x0FD39: ["<medial>", 0x0634, 0x062E],
    0x0FD3A: ["<medial>", 0x0637, 0x0645],
    0x0FD3B: ["<medial>", 0x0638, 0x0645],
    0x0FD3C: ["<final>", 0x0627, 0x064B],
    0x0FD3D: ["<isolated>", 0x0627, 0x064B],
    0x0FD50: ["<initial>", 0x062A, 0x062C, 0x0645],
    0x0FD51: ["<final>", 0x062A, 0x062D, 0x062C],
    0x0FD52: ["<initial>", 0x062A, 0x062D, 0x062C],
    0x0FD53: ["<initial>", 0x062A, 0x062D, 0x0645],
    0x0FD54: ["<initial>", 0x062A, 0x062E, 0x0645],
    0x0FD55: ["<initial>", 0x062A, 0x0645, 0x062C],
    0x0FD56: ["<initial>", 0x062A, 0x0645, 0x062D],
    0x0FD57: ["<initial>", 0x062A, 0x0645, 0x062E],
    0x0FD58: ["<final>", 0x062C, 0x0645, 0x062D],
    0x0FD59: ["<initial>", 0x062C, 0x0645, 0x062D],
    0x0FD5A: ["<final>", 0x062D, 0x0645, 0x064A],
    0x0FD5B: ["<final>", 0x062D, 0x0645, 0x0649],
    0x0FD5C: ["<initial>", 0x0633, 0x062D, 0x062C],
    0x0FD5D: ["<initial>", 0x0633, 0x062C, 0x062D],
    0x0FD5E: ["<final>", 0x0633, 0x062C, 0x0649],
    0x0FD5F: ["<final>", 0x0633, 0x0645, 0x062D],
    0x0FD60: ["<initial>", 0x0633, 0x0645, 0x062D],
    0x0FD61: ["<initial>", 0x0633, 0x0645, 0x062C],
    0x0FD62: ["<final>", 0x0633, 0x0645, 0x0645],
    0x0FD63: ["<initial>", 0x0633, 0x0645, 0x0645],
    0x0FD64: ["<final>", 0x0635, 0x062D, 0x062D],
    0x0FD65: ["<initial>", 0x0635, 0x062D, 0x062D],
    0x0FD66: ["<final>", 0x0635, 0x0645, 0x0645],
    0x0FD67: ["<final>", 0x0634, 0x062D, 0x0645],
    0x0FD68: ["<initial>", 0x0634, 0x062D, 0x0645],
    0x0FD69: ["<final>", 0x0634, 0x062C, 0x064A],
    0x0FD6A: ["<final>", 0x0634, 0x0645, 0x062E],
    0x0FD6B: ["<initial>", 0x0634, 0x0645, 0x062E],
    0x0FD6C: ["<final>", 0x0634, 0x0645, 0x0645],
    0x0FD6D: ["<initial>", 0x0634, 0x0645, 0x0645],
    0x0FD6E: ["<final>", 0x0636, 0x062D, 0x0649],
    0x0FD6F: ["<final>", 0x0636, 0x062E, 0x0645],
    0x0FD70: ["<initial>", 0x0636, 0x062E, 0x0645],
    0x0FD71: ["<final>", 0x0637, 0x0645, 0x062D],
    0x0FD72: ["<initial>", 0x0637, 0x0645, 0x062D],
    0x0FD73: ["<initial>", 0x0637, 0x0645, 0x0645],
    0x0FD74: ["<final>", 0x0637, 0x0645, 0x064A],
    0x0FD75: ["<final>", 0x0639, 0x062C, 0x0645],
    0x0FD76: ["<final>", 0x0639, 0x0645, 0x0645],
    0x0FD77: ["<initial>", 0x0639, 0x0645, 0x0645],
    0x0FD78: ["<final>", 0x0639, 0x0645, 0x0649],
    0x0FD79: ["<final>", 0x063A, 0x0645, 0x0645],
    0x0FD7A: ["<final>", 0x063A, 0x0645, 0x064A],
    0x0FD7B: ["<final>", 0x063A, 0x0645, 0x0649],
    0x0FD7C: ["<final>", 0x0641, 0x062E, 0x0645],
    0x0FD7D: ["<initial>", 0x0641, 0x062E, 0x0645],
    0x0FD7E: ["<final>", 0x0642, 0x0645, 0x062D],
    0x0FD7F: ["<final>", 0x0642, 0x0645, 0x0645],
    0x0FD80: ["<final>", 0x0644, 0x062D, 0x0645],
    0x0FD81: ["<final>", 0x0644, 0x062D, 0x064A],
    0x0FD82: ["<final>", 0x0644, 0x062D, 0x0649],
    0x0FD83: ["<initial>", 0x0644, 0x062C, 0x062C],
    0x0FD84: ["<final>", 0x0644, 0x062C, 0x062C],
    0x0FD85: ["<final>", 0x0644, 0x062E, 0x0645],
    0x0FD86: ["<initial>", 0x0644, 0x062E, 0x0645],
    0x0FD87: ["<final>", 0x0644, 0x0645, 0x062D],
    0x0FD88: ["<initial>", 0x0644, 0x0645, 0x062D],
    0x0FD89: ["<initial>", 0x0645, 0x062D, 0x062C],
    0x0FD8A: ["<initial>", 0x0645, 0x062D, 0x0645],
    0x0FD8B: ["<final>", 0x0645, 0x062D, 0x064A],
    0x0FD8C: ["<initial>", 0x0645, 0x062C, 0x062D],
    0x0FD8D: ["<initial>", 0x0645, 0x062C, 0x0645],
    0x0FD8E: ["<initial>", 0x0645, 0x062E, 0x062C],
    0x0FD8F: ["<initial>", 0x0645, 0x062E, 0x0645],
    0x0FD92: ["<initial>", 0x0645, 0x062C, 0x062E],
    0x0FD93: ["<initial>", 0x0647, 0x0645, 0x062C],
    0x0FD94: ["<initial>", 0x0647, 0x0645, 0x0645],
    0x0FD95: ["<initial>", 0x0646, 0x062D, 0x0645],
    0x0FD96: ["<final>", 0x0646, 0x062D, 0x0649],
    0x0FD97: ["<final>", 0x0646, 0x062C, 0x0645],
    0x0FD98: ["<initial>", 0x0646, 0x062C, 0x0645],
    0x0FD99: ["<final>", 0x0646, 0x062C, 0x0649],
    0x0FD9A: ["<final>", 0x0646, 0x0645, 0x064A],
    0x0FD9B: ["<final>", 0x0646, 0x0645, 0x0649],
    0x0FD9C: ["<final>", 0x064A, 0x0645, 0x0645],
    0x0FD9D: ["<initial>", 0x064A, 0x0645, 0x0645],
    0x0FD9E: ["<final>", 0x0628, 0x062E, 0x064A],
    0x0FD9F: ["<final>", 0x062A, 0x062C, 0x064A],
    0x0FDA0: ["<final>", 0x062A, 0x062C, 0x0649],
    0x0FDA1: ["<final>", 0x062A, 0x062E, 0x064A],
    0x0FDA2: ["<final>", 0x062A, 0x062E, 0x0649],
    0x0FDA3: ["<final>", 0x062A, 0x0645, 0x064A],
    0x0FDA4: ["<final>", 0x062A, 0x0645, 0x0649],
    0x0FDA5: ["<final>", 0x062C, 0x0645, 0x064A],
    0x0FDA6: ["<final>", 0x062C, 0x062D, 0x0649],
    0x0FDA7: ["<final>", 0x062C, 0x0645, 0x0649],
    0x0FDA8: ["<final>", 0x0633, 0x062E, 0x0649],
    0x0FDA9: ["<final>", 0x0635, 0x062D, 0x064A],
    0x0FDAA: ["<final>", 0x0634, 0x062D, 0x064A],
    0x0FDAB: ["<final>", 0x0636, 0x062D, 0x064A],
    0x0FDAC: ["<final>", 0x0644, 0x062C, 0x064A],
    0x0FDAD: ["<final>", 0x0644, 0x0645, 0x064A],
    0x0FDAE: ["<final>", 0x064A, 0x062D, 0x064A],
    0x0FDAF: ["<final>", 0x064A, 0x062C, 0x064A],
    0x0FDB0: ["<final>", 0x064A, 0x0645, 0x064A],
    0x0FDB1: ["<final>", 0x0645, 0x0645, 0x064A],
    0x0FDB2: ["<final>", 0x0642, 0x0645, 0x064A],
    0x0FDB3: ["<final>", 0x0646, 0x062D, 0x064A],
    0x0FDB4: ["<initial>", 0x0642, 0x0645, 0x062D],
    0x0FDB5: ["<initial>", 0x0644, 0x062D, 0x0645],
    0x0FDB6: ["<final>", 0x0639, 0x0645, 0x064A],
    0x0FDB7: ["<final>", 0x0643, 0x0645, 0x064A],
    0x0FDB8: ["<initial>", 0x0646, 0x062C, 0x062D],
    0x0FDB9: ["<final>", 0x0645, 0x062E, 0x064A],
    0x0FDBA: ["<initial>", 0x0644, 0x062C, 0x0645],
    0x0FDBB: ["<final>", 0x0643, 0x0645, 0x0645],
    0x0FDBC: ["<final>", 0x0644, 0x062C, 0x0645],
    0x0FDBD: ["<final>", 0x0646, 0x062C, 0x062D],
    0x0FDBE: ["<final>", 0x062C, 0x062D, 0x064A],
    0x0FDBF: ["<final>", 0x062D, 0x062C, 0x064A],
    0x0FDC0: ["<final>", 0x0645, 0x062C, 0x064A],
    0x0FDC1: ["<final>", 0x0641, 0x0645, 0x064A],
    0x0FDC2: ["<final>", 0x0628, 0x062D, 0x064A],
    0x0FDC3: ["<initial>", 0x0643, 0x0645, 0x0645],
    0x0FDC4: ["<initial>", 0x0639, 0x062C, 0x0645],
    0x0FDC5: ["<initial>", 0x0635, 0x0645, 0x0645],
    0x0FDC6: ["<final>", 0x0633, 0x062E, 0x064A],
    0x0FDC7: ["<final>", 0x0646, 0x062C, 0x064A],
    0x0FDF0: ["<isolated>", 0x0635, 0x0644, 0x06D2],
    0x0FDF1: ["<isolated>", 0x0642, 0x0644, 0x06D2],
    0x0FDF2: ["<isolated>", 0x0627, 0x0644, 0x0644, 0x0647],
    0x0FDF3: ["<isolated>", 0x0627, 0x0643, 0x0628, 0x0631],
    0x0FDF4: ["<isolated>", 0x0645, 0x062D, 0x0645, 0x062F],
    0x0FDF5: ["<isolated>", 0x0635, 0x0644, 0x0639, 0x0645],
    0x0FDF6: ["<isolated>", 0x0631, 0x0633, 0x0648, 0x0644],
    0x0FDF7: ["<isolated>", 0x0639, 0x0644, 0x064A, 0x0647],
    0x0FDF8: ["<isolated>", 0x0648, 0x0633, 0x0644, 0x0645],
    0x0FDF9: ["<isolated>", 0x0635, 0x0644, 0x0649],
    0x0FDFA: ["<isolated>", 0x0635, 0x0644, 0x0649, 0x0020, 0x0627, 0x0644, 0x0644, 0x0647, 0x0020, 0x0639, 0x0644, 0x064A, 0x0647, 0x0020, 0x0648, 0x0633, 0x0644, 0x0645],
    0x0FDFB: ["<isolated>", 0x062C, 0x0644, 0x0020, 0x062C, 0x0644, 0x0627, 0x0644, 0x0647],
    0x0FDFC: ["<isolated>", 0x0631, 0x06CC, 0x0627, 0x0644],
    0x0FE10: ["<vertical>", 0x002C],
    0x0FE11: ["<vertical>", 0x3001],
    0x0FE12: ["<vertical>", 0x3002],
    0x0FE13: ["<vertical>", 0x003A],
    0x0FE14: ["<vertical>", 0x003B],
    0x0FE15: ["<vertical>", 0x0021],
    0x0FE16: ["<vertical>", 0x003F],
    0x0FE17: ["<vertical>", 0x3016],
    0x0FE18: ["<vertical>", 0x3017],
    0x0FE19: ["<vertical>", 0x2026],
    0x0FE30: ["<vertical>", 0x2025],
    0x0FE31: ["<vertical>", 0x2014],
    0x0FE32: ["<vertical>", 0x2013],
    0x0FE33: ["<vertical>", 0x005F],
    0x0FE34: ["<vertical>", 0x005F],
    0x0FE35: ["<vertical>", 0x0028],
    0x0FE36: ["<vertical>", 0x0029],
    0x0FE37: ["<vertical>", 0x007B],
    0x0FE38: ["<vertical>", 0x007D],
    0x0FE39: ["<vertical>", 0x3014],
    0x0FE3A: ["<vertical>", 0x3015],
    0x0FE3B: ["<vertical>", 0x3010],
    0x0FE3C: ["<vertical>", 0x3011],
    0x0FE3D: ["<vertical>", 0x300A],
    0x0FE3E: ["<vertical>", 0x300B],
    0x0FE3F: ["<vertical>", 0x3008],
    0x0FE40: ["<vertical>", 0x3009],
    0x0FE41: ["<vertical>", 0x300C],
    0x0FE42: ["<vertical>", 0x300D],
    0x0FE43: ["<vertical>", 0x300E],
    0x0FE44: ["<vertical>", 0x300F],
    0x0FE47: ["<vertical>", 0x005B],
    0x0FE48: ["<vertical>", 0x005D],
    0x0FE49: ["<compat>", 0x203E],
    0x0FE4A: ["<compat>", 0x203E],
    0x0FE4B: ["<compat>", 0x203E],
    0x0FE4C: ["<compat>", 0x203E],
    0x0FE4D: ["<compat>", 0x005F],
    0x0FE4E: ["<compat>", 0x005F],
    0x0FE4F: ["<compat>", 0x005F],
    0x0FE50: ["<small>", 0x002C],
    0x0FE51: ["<small>", 0x3001],
    0x0FE52: ["<small>", 0x002E],
    0x0FE54: ["<small>", 0x003B],
    0x0FE55: ["<small>", 0x003A],
    0x0FE56: ["<small>", 0x003F],
    0x0FE57: ["<small>", 0x0021],
    0x0FE58: ["<small>", 0x2014],
    0x0FE59: ["<small>", 0x0028],
    0x0FE5A: ["<small>", 0x0029],
    0x0FE5B: ["<small>", 0x007B],
    0x0FE5C: ["<small>", 0x007D],
    0x0FE5D: ["<small>", 0x3014],
    0x0FE5E: ["<small>", 0x3015],
    0x0FE5F: ["<small>", 0x0023],
    0x0FE60: ["<small>", 0x0026],
    0x0FE61: ["<small>", 0x002A],
    0x0FE62: ["<small>", 0x002B],
    0x0FE63: ["<small>", 0x002D],
    0x0FE64: ["<small>", 0x003C],
    0x0FE65: ["<small>", 0x003E],
    0x0FE66: ["<small>", 0x003D],
    0x0FE68: ["<small>", 0x005C],
    0x0FE69: ["<small>", 0x0024],
    0x0FE6A: ["<small>", 0x0025],
    0x0FE6B: ["<small>", 0x0040],
    0x0FE70: ["<isolated>", 0x0020, 0x064B],
    0x0FE71: ["<medial>", 0x0640, 0x064B],
    0x0FE72: ["<isolated>", 0x0020, 0x064C],
    0x0FE74: ["<isolated>", 0x0020, 0x064D],
    0x0FE76: ["<isolated>", 0x0020, 0x064E],
    0x0FE77: ["<medial>", 0x0640, 0x064E],
    0x0FE78: ["<isolated>", 0x0020, 0x064F],
    0x0FE79: ["<medial>", 0x0640, 0x064F],
    0x0FE7A: ["<isolated>", 0x0020, 0x0650],
    0x0FE7B: ["<medial>", 0x0640, 0x0650],
    0x0FE7C: ["<isolated>", 0x0020, 0x0651],
    0x0FE7D: ["<medial>", 0x0640, 0x0651],
    0x0FE7E: ["<isolated>", 0x0020, 0x0652],
    0x0FE7F: ["<medial>", 0x0640, 0x0652],
    0x0FE80: ["<isolated>", 0x0621],
    0x0FE81: ["<isolated>", 0x0622],
    0x0FE82: ["<final>", 0x0622],
    0x0FE83: ["<isolated>", 0x0623],
    0x0FE84: ["<final>", 0x0623],
    0x0FE85: ["<isolated>", 0x0624],
    0x0FE86: ["<final>", 0x0624],
    0x0FE87: ["<isolated>", 0x0625],
    0x0FE88: ["<final>", 0x0625],
    0x0FE89: ["<isolated>", 0x0626],
    0x0FE8A: ["<final>", 0x0626],
    0x0FE8B: ["<initial>", 0x0626],
    0x0FE8C: ["<medial>", 0x0626],
    0x0FE8D: ["<isolated>", 0x0627],
    0x0FE8E: ["<final>", 0x0627],
    0x0FE8F: ["<isolated>", 0x0628],
    0x0FE90: ["<final>", 0x0628],
    0x0FE91: ["<initial>", 0x0628],
    0x0FE92: ["<medial>", 0x0628],
    0x0FE93: ["<isolated>", 0x0629],
    0x0FE94: ["<final>", 0x0629],
    0x0FE95: ["<isolated>", 0x062A],
    0x0FE96: ["<final>", 0x062A],
    0x0FE97: ["<initial>", 0x062A],
    0x0FE98: ["<medial>", 0x062A],
    0x0FE99: ["<isolated>", 0x062B],
    0x0FE9A: ["<final>", 0x062B],
    0x0FE9B: ["<initial>", 0x062B],
    0x0FE9C: ["<medial>", 0x062B],
    0x0FE9D: ["<isolated>", 0x062C],
    0x0FE9E: ["<final>", 0x062C],
    0x0FE9F: ["<initial>", 0x062C],
    0x0FEA0: ["<medial>", 0x062C],
    0x0FEA1: ["<isolated>", 0x062D],
    0x0FEA2: ["<final>", 0x062D],
    0x0FEA3: ["<initial>", 0x062D],
    0x0FEA4: ["<medial>", 0x062D],
    0x0FEA5: ["<isolated>", 0x062E],
    0x0FEA6: ["<final>", 0x062E],
    0x0FEA7: ["<initial>", 0x062E],
    0x0FEA8: ["<medial>", 0x062E],
    0x0FEA9: ["<isolated>", 0x062F],
    0x0FEAA: ["<final>", 0x062F],
    0x0FEAB: ["<isolated>", 0x0630],
    0x0FEAC: ["<final>", 0x0630],
    0x0FEAD: ["<isolated>", 0x0631],
    0x0FEAE: ["<final>", 0x0631],
    0x0FEAF: ["<isolated>", 0x0632],
    0x0FEB0: ["<final>", 0x0632],
    0x0FEB1: ["<isolated>", 0x0633],
    0x0FEB2: ["<final>", 0x0633],
    0x0FEB3: ["<initial>", 0x0633],
    0x0FEB4: ["<medial>", 0x0633],
    0x0FEB5: ["<isolated>", 0x0634],
    0x0FEB6: ["<final>", 0x0634],
    0x0FEB7: ["<initial>", 0x0634],
    0x0FEB8: ["<medial>", 0x0634],
    0x0FEB9: ["<isolated>", 0x0635],
    0x0FEBA: ["<final>", 0x0635],
    0x0FEBB: ["<initial>", 0x0635],
    0x0FEBC: ["<medial>", 0x0635],
    0x0FEBD: ["<isolated>", 0x0636],
    0x0FEBE: ["<final>", 0x0636],
    0x0FEBF: ["<initial>", 0x0636],
    0x0FEC0: ["<medial>", 0x0636],
    0x0FEC1: ["<isolated>", 0x0637],
    0x0FEC2: ["<final>", 0x0637],
    0x0FEC3: ["<initial>", 0x0637],
    0x0FEC4: ["<medial>", 0x0637],
    0x0FEC5: ["<isolated>", 0x0638],
    0x0FEC6: ["<final>", 0x0638],
    0x0FEC7: ["<initial>", 0x0638],
    0x0FEC8: ["<medial>", 0x0638],
    0x0FEC9: ["<isolated>", 0x0639],
    0x0FECA: ["<final>", 0x0639],
    0x0FECB: ["<initial>", 0x0639],
    0x0FECC: ["<medial>", 0x0639],
    0x0FECD: ["<isolated>", 0x063A],
    0x0FECE: ["<final>", 0x063A],
    0x0FECF: ["<initial>", 0x063A],
    0x0FED0: ["<medial>", 0x063A],
    0x0FED1: ["<isolated>", 0x0641],
    0x0FED2: ["<final>", 0x0641],
    0x0FED3: ["<initial>", 0x0641],
    0x0FED4: ["<medial>", 0x0641],
    0x0FED5: ["<isolated>", 0x0642],
    0x0FED6: ["<final>", 0x0642],
    0x0FED7: ["<initial>", 0x0642],
    0x0FED8: ["<medial>", 0x0642],
    0x0FED9: ["<isolated>", 0x0643],
    0x0FEDA: ["<final>", 0x0643],
    0x0FEDB: ["<initial>", 0x0643],
    0x0FEDC: ["<medial>", 0x0643],
    0x0FEDD: ["<isolated>", 0x0644],
    0x0FEDE: ["<final>", 0x0644],
    0x0FEDF: ["<initial>", 0x0644],
    0x0FEE0: ["<medial>", 0x0644],
    0x0FEE1: ["<isolated>", 0x0645],
    0x0FEE2: ["<final>", 0x0645],
    0x0FEE3: ["<initial>", 0x0645],
    0x0FEE4: ["<medial>", 0x0645],
    0x0FEE5: ["<isolated>", 0x0646],
    0x0FEE6: ["<final>", 0x0646],
    0x0FEE7: ["<initial>", 0x0646],
    0x0FEE8: ["<medial>", 0x0646],
    0x0FEE9: ["<isolated>", 0x0647],
    0x0FEEA: ["<final>", 0x0647],
    0x0FEEB: ["<initial>", 0x0647],
    0x0FEEC: ["<medial>", 0x0647],
    0x0FEED: ["<isolated>", 0x0648],
    0x0FEEE: ["<final>", 0x0648],
    0x0FEEF: ["<isolated>", 0x0649],
    0x0FEF0: ["<final>", 0x0649],
    0x0FEF1: ["<isolated>", 0x064A],
    0x0FEF2: ["<final>", 0x064A],
    0x0FEF3: ["<initial>", 0x064A],
    0x0FEF4: ["<medial>", 0x064A],
    0x0FEF5: ["<isolated>", 0x0644, 0x0622],
    0x0FEF6: ["<final>", 0x0644, 0x0622],
    0x0FEF7: ["<isolated>", 0x0644, 0x0623],
    0x0FEF8: ["<final>", 0x0644, 0x0623],
    0x0FEF9: ["<isolated>", 0x0644, 0x0625],
    0x0FEFA: ["<final>", 0x0644, 0x0625],
    0x0FEFB: ["<isolated>", 0x0644, 0x0627],
    0x0FEFC: ["<final>", 0x0644, 0x0627],
    0x0FF01: ["<wide>", 0x0021],
    0x0FF02: ["<wide>", 0x0022],
    0x0FF03: ["<wide>", 0x0023],
    0x0FF04: ["<wide>", 0x0024],
    0x0FF05: ["<wide>", 0x0025],
    0x0FF06: ["<wide>", 0x0026],
    0x0FF07: ["<wide>", 0x0027],
    0x0FF08: ["<wide>", 0x0028],
    0x0FF09: ["<wide>", 0x0029],
    0x0FF0A: ["<wide>", 0x002A],
    0x0FF0B: ["<wide>", 0x002B],
    0x0FF0C: ["<wide>", 0x002C],
    0x0FF0D: ["<wide>", 0x002D],
    0x0FF0E: ["<wide>", 0x002E],
    0x0FF0F: ["<wide>", 0x002F],
    0x0FF10: ["<wide>", 0x0030],
    0x0FF11: ["<wide>", 0x0031],
    0x0FF12: ["<wide>", 0x0032],
    0x0FF13: ["<wide>", 0x0033],
    0x0FF14: ["<wide>", 0x0034],
    0x0FF15: ["<wide>", 0x0035],
    0x0FF16: ["<wide>", 0x0036],
    0x0FF17: ["<wide>", 0x0037],
    0x0FF18: ["<wide>", 0x0038],
    0x0FF19: ["<wide>", 0x0039],
    0x0FF1A: ["<wide>", 0x003A],
    0x0FF1B: ["<wide>", 0x003B],
    0x0FF1C: ["<wide>", 0x003C],
    0x0FF1D: ["<wide>", 0x003D],
    0x0FF1E: ["<wide>", 0x003E],
    0x0FF1F: ["<wide>", 0x003F],
    0x0FF20: ["<wide>", 0x0040],
    0x0FF21: ["<wide>", 0x0041],
    0x0FF22: ["<wide>", 0x0042],
    0x0FF23: ["<wide>", 0x0043],
    0x0FF24: ["<wide>", 0x0044],
    0x0FF25: ["<wide>", 0x0045],
    0x0FF26: ["<wide>", 0x0046],
    0x0FF27: ["<wide>", 0x0047],
    0x0FF28: ["<wide>", 0x0048],
    0x0FF29: ["<wide>", 0x0049],
    0x0FF2A: ["<wide>", 0x004A],
    0x0FF2B: ["<wide>", 0x004B],
    0x0FF2C: ["<wide>", 0x004C],
    0x0FF2D: ["<wide>", 0x004D],
    0x0FF2E: ["<wide>", 0x004E],
    0x0FF2F: ["<wide>", 0x004F],
    0x0FF30: ["<wide>", 0x0050],
    0x0FF31: ["<wide>", 0x0051],
    0x0FF32: ["<wide>", 0x0052],
    0x0FF33: ["<wide>", 0x0053],
    0x0FF34: ["<wide>", 0x0054],
    0x0FF35: ["<wide>", 0x0055],
    0x0FF36: ["<wide>", 0x0056],
    0x0FF37: ["<wide>", 0x0057],
    0x0FF38: ["<wide>", 0x0058],
    0x0FF39: ["<wide>", 0x0059],
    0x0FF3A: ["<wide>", 0x005A],
    0x0FF3B: ["<wide>", 0x005B],
    0x0FF3C: ["<wide>", 0x005C],
    0x0FF3D: ["<wide>", 0x005D],
    0x0FF3E: ["<wide>", 0x005E],
    0x0FF3F: ["<wide>", 0x005F],
    0x0FF40: ["<wide>", 0x0060],
    0x0FF41: ["<wide>", 0x0061],
    0x0FF42: ["<wide>", 0x0062],
    0x0FF43: ["<wide>", 0x0063],
    0x0FF44: ["<wide>", 0x0064],
    0x0FF45: ["<wide>", 0x0065],
    0x0FF46: ["<wide>", 0x0066],
    0x0FF47: ["<wide>", 0x0067],
    0x0FF48: ["<wide>", 0x0068],
    0x0FF49: ["<wide>", 0x0069],
    0x0FF4A: ["<wide>", 0x006A],
    0x0FF4B: ["<wide>", 0x006B],
    0x0FF4C: ["<wide>", 0x006C],
    0x0FF4D: ["<wide>", 0x006D],
    0x0FF4E: ["<wide>", 0x006E],
    0x0FF4F: ["<wide>", 0x006F],
    0x0FF50: ["<wide>", 0x0070],
    0x0FF51: ["<wide>", 0x0071],
    0x0FF52: ["<wide>", 0x0072],
    0x0FF53: ["<wide>", 0x0073],
    0x0FF54: ["<wide>", 0x0074],
    0x0FF55: ["<wide>", 0x0075],
    0x0FF56: ["<wide>", 0x0076],
    0x0FF57: ["<wide>", 0x0077],
    0x0FF58: ["<wide>", 0x0078],
    0x0FF59: ["<wide>", 0x0079],
    0x0FF5A: ["<wide>", 0x007A],
    0x0FF5B: ["<wide>", 0x007B],
    0x0FF5C: ["<wide>", 0x007C],
    0x0FF5D: ["<wide>", 0x007D],
    0x0FF5E: ["<wide>", 0x007E],
    0x0FF5F: ["<wide>", 0x2985],
    0x0FF60: ["<wide>", 0x2986],
    0x0FF61: ["<narrow>", 0x3002],
    0x0FF62: ["<narrow>", 0x300C],
    0x0FF63: ["<narrow>", 0x300D],
    0x0FF64: ["<narrow>", 0x3001],
    0x0FF65: ["<narrow>", 0x30FB],
    0x0FF66: ["<narrow>", 0x30F2],
    0x0FF67: ["<narrow>", 0x30A1],
    0x0FF68: ["<narrow>", 0x30A3],
    0x0FF69: ["<narrow>", 0x30A5],
    0x0FF6A: ["<narrow>", 0x30A7],
    0x0FF6B: ["<narrow>", 0x30A9],
    0x0FF6C: ["<narrow>", 0x30E3],
    0x0FF6D: ["<narrow>", 0x30E5],
    0x0FF6E: ["<narrow>", 0x30E7],
    0x0FF6F: ["<narrow>", 0x30C3],
    0x0FF70: ["<narrow>", 0x30FC],
    0x0FF71: ["<narrow>", 0x30A2],
    0x0FF72: ["<narrow>", 0x30A4],
    0x0FF73: ["<narrow>", 0x30A6],
    0x0FF74: ["<narrow>", 0x30A8],
    0x0FF75: ["<narrow>", 0x30AA],
    0x0FF76: ["<narrow>", 0x30AB],
    0x0FF77: ["<narrow>", 0x30AD],
    0x0FF78: ["<narrow>", 0x30AF],
    0x0FF79: ["<narrow>", 0x30B1],
    0x0FF7A: ["<narrow>", 0x30B3],
    0x0FF7B: ["<narrow>", 0x30B5],
    0x0FF7C: ["<narrow>", 0x30B7],
    0x0FF7D: ["<narrow>", 0x30B9],
    0x0FF7E: ["<narrow>", 0x30BB],
    0x0FF7F: ["<narrow>", 0x30BD],
    0x0FF80: ["<narrow>", 0x30BF],
    0x0FF81: ["<narrow>", 0x30C1],
    0x0FF82: ["<narrow>", 0x30C4],
    0x0FF83: ["<narrow>", 0x30C6],
    0x0FF84: ["<narrow>", 0x30C8],
    0x0FF85: ["<narrow>", 0x30CA],
    0x0FF86: ["<narrow>", 0x30CB],
    0x0FF87: ["<narrow>", 0x30CC],
    0x0FF88: ["<narrow>", 0x30CD],
    0x0FF89: ["<narrow>", 0x30CE],
    0x0FF8A: ["<narrow>", 0x30CF],
    0x0FF8B: ["<narrow>", 0x30D2],
    0x0FF8C: ["<narrow>", 0x30D5],
    0x0FF8D: ["<narrow>", 0x30D8],
    0x0FF8E: ["<narrow>", 0x30DB],
    0x0FF8F: ["<narrow>", 0x30DE],
    0x0FF90: ["<narrow>", 0x30DF],
    0x0FF91: ["<narrow>", 0x30E0],
    0x0FF92: ["<narrow>", 0x30E1],
    0x0FF93: ["<narrow>", 0x30E2],
    0x0FF94: ["<narrow>", 0x30E4],
    0x0FF95: ["<narrow>", 0x30E6],
    0x0FF96: ["<narrow>", 0x30E8],
    0x0FF97: ["<narrow>", 0x30E9],
    0x0FF98: ["<narrow>", 0x30EA],
    0x0FF99: ["<narrow>", 0x30EB],
    0x0FF9A: ["<narrow>", 0x30EC],
    0x0FF9B: ["<narrow>", 0x30ED],
    0x0FF9C: ["<narrow>", 0x30EF],
    0x0FF9D: ["<narrow>", 0x30F3],
    0x0FF9E: ["<narrow>", 0x3099],
    0x0FF9F: ["<narrow>", 0x309A],
    0x0FFA0: ["<narrow>", 0x3164],
    0x0FFA1: ["<narrow>", 0x3131],
    0x0FFA2: ["<narrow>", 0x3132],
    0x0FFA3: ["<narrow>", 0x3133],
    0x0FFA4: ["<narrow>", 0x3134],
    0x0FFA5: ["<narrow>", 0x3135],
    0x0FFA6: ["<narrow>", 0x3136],
    0x0FFA7: ["<narrow>", 0x3137],
    0x0FFA8: ["<narrow>", 0x3138],
    0x0FFA9: ["<narrow>", 0x3139],
    0x0FFAA: ["<narrow>", 0x313A],
    0x0FFAB: ["<narrow>", 0x313B],
    0x0FFAC: ["<narrow>", 0x313C],
    0x0FFAD: ["<narrow>", 0x313D],
    0x0FFAE: ["<narrow>", 0x313E],
    0x0FFAF: ["<narrow>", 0x313F],
    0x0FFB0: ["<narrow>", 0x3140],
    0x0FFB1: ["<narrow>", 0x3141],
    0x0FFB2: ["<narrow>", 0x3142],
    0x0FFB3: ["<narrow>", 0x3143],
    0x0FFB4: ["<narrow>", 0x3144],
    0x0FFB5: ["<narrow>", 0x3145],
    0x0FFB6: ["<narrow>", 0x3146],
    0x0FFB7: ["<narrow>", 0x3147],
    0x0FFB8: ["<narrow>", 0x3148],
    0x0FFB9: ["<narrow>", 0x3149],
    0x0FFBA: ["<narrow>", 0x314A],
    0x0FFBB: ["<narrow>", 0x314B],
    0x0FFBC: ["<narrow>", 0x314C],
    0x0FFBD: ["<narrow>", 0x314D],
    0x0FFBE: ["<narrow>", 0x314E],
    0x0FFC2: ["<narrow>", 0x314F],
    0x0FFC3: ["<narrow>", 0x3150],
    0x0FFC4: ["<narrow>", 0x3151],
    0x0FFC5: ["<narrow>", 0x3152],
    0x0FFC6: ["<narrow>", 0x3153],
    0x0FFC7: ["<narrow>", 0x3154],
    0x0FFCA: ["<narrow>", 0x3155],
    0x0FFCB: ["<narrow>", 0x3156],
    0x0FFCC: ["<narrow>", 0x3157],
    0x0FFCD: ["<narrow>", 0x3158],
    0x0FFCE: ["<narrow>", 0x3159],
    0x0FFCF: ["<narrow>", 0x315A],
    0x0FFD2: ["<narrow>", 0x315B],
    0x0FFD3: ["<narrow>", 0x315C],
    0x0FFD4: ["<narrow>", 0x315D],
    0x0FFD5: ["<narrow>", 0x315E],
    0x0FFD6: ["<narrow>", 0x315F],
    0x0FFD7: ["<narrow>", 0x3160],
    0x0FFDA: ["<narrow>", 0x3161],
    0x0FFDB: ["<narrow>", 0x3162],
    0x0FFDC: ["<narrow>", 0x3163],
    0x0FFE0: ["<wide>", 0x00A2],
    0x0FFE1: ["<wide>", 0x00A3],
    0x0FFE2: ["<wide>", 0x00AC],
    0x0FFE3: ["<wide>", 0x00AF],
    0x0FFE4: ["<wide>", 0x00A6],
    0x0FFE5: ["<wide>", 0x00A5],
    0x0FFE6: ["<wide>", 0x20A9],
    0x0FFE8: ["<narrow>", 0x2502],
    0x0FFE9: ["<narrow>", 0x2190],
    0x0FFEA: ["<narrow>", 0x2191],
    0x0FFEB: ["<narrow>", 0x2192],
    0x0FFEC: ["<narrow>", 0x2193],
    0x0FFED: ["<narrow>", 0x25A0],
    0x0FFEE: ["<narrow>", 0x25CB],
    0x105C9: [0x105D2, 0x0307],
    0x105E4: [0x105DA, 0x0307],
    0x10781: ["<super>", 0x02D0],
    0x10782: ["<super>", 0x02D1],
    0x10783: ["<super>", 0x00E6],
    0x10784: ["<super>", 0x0299],
    0x10785: ["<super>", 0x0253],
    0x10787: ["<super>", 0x02A3],
    0x10788: ["<super>", 0xAB66],
    0x10789: ["<super>", 0x02A5],
    0x1078A: ["<super>", 0x02A4],
    0x1078B: ["<super>", 0x0256],
    0x1078C: ["<super>", 0x0257],
    0x1078D: ["<super>", 0x1D91],
    0x1078E: ["<super>", 0x0258],
    0x1078F: ["<super>", 0x025E],
    0x10790: ["<super>", 0x02A9],
    0x10791: ["<super>", 0x0264],
    0x10792: ["<super>", 0x0262],
    0x10793: ["<super>", 0x0260],
    0x10794: ["<super>", 0x029B],
    0x10795: ["<super>", 0x0127],
    0x10796: ["<super>", 0x029C],
    0x10797: ["<super>", 0x0267],
    0x10798: ["<super>", 0x0284],
    0x10799: ["<super>", 0x02AA],
    0x1079A: ["<super>", 0x02AB],
    0x1079B: ["<super>", 0x026C],
    0x1079C: ["<super>", 0x1DF04],
    0x1079D: ["<super>", 0xA78E],
    0x1079E: ["<super>", 0x026E],
    0x1079F: ["<super>", 0x1DF05],
    0x107A0: ["<super>", 0x028E],
    0x107A1: ["<super>", 0x1DF06],
    0x107A2: ["<super>", 0x00F8],
    0x107A3: ["<super>", 0x0276],
    0x107A4: ["<super>", 0x0277],
    0x107A5: ["<super>", 0x0071],
    0x107A6: ["<super>", 0x027A],
    0x107A7: ["<super>", 0x1DF08],
    0x107A8: ["<super>", 0x027D],
    0x107A9: ["<super>", 0x027E],
    0x107AA: ["<super>", 0x0280],
    0x107AB: ["<super>", 0x02A8],
    0x107AC: ["<super>", 0x02A6],
    0x107AD: ["<super>", 0xAB67],
    0x107AE: ["<super>", 0x02A7],
    0x107AF: ["<super>", 0x0288],
    0x107B0: ["<super>", 0x2C71],
    0x107B2: ["<super>", 0x028F],
    0x107B3: ["<super>", 0x02A1],
    0x107B4: ["<super>", 0x02A2],
    0x107B5: ["<super>", 0x0298],
    0x107B6: ["<super>", 0x01C0],
    0x107B7: ["<super>", 0x01C1],
    0x107B8: ["<super>", 0x01C2],
    0x107B9: ["<super>", 0x1DF0A],
    0x107BA: ["<super>", 0x1DF1E],
    0x1109A: [0x11099, 0x110BA],
    0x1109C: [0x1109B, 0x110BA],
    0x110AB: [0x110A5, 0x110BA],
    0x1112E: [0x11131, 0x11127],
    0x1112F: [0x11132, 0x11127],
    0x1134B: [0x11347, 0x1133E],
    0x1134C: [0x11347, 0x11357],
    0x11383: [0x11382, 0x113C9],
    0x11385: [0x11384, 0x113BB],
    0x1138E: [0x1138B, 0x113C2],
    0x11391: [0x11390, 0x113C9],
    0x113C5: [0x113C2, 0x113C2],
    0x113C7: [0x113C2, 0x113B8],
    0x113C8: [0x113C2, 0x113C9],
    0x114BB: [0x114B9, 0x114BA],
    0x114BC: [0x114B9, 0x114B0],
    0x114BE: [0x114B9, 0x114BD],
    0x115BA: [0x115B8, 0x115AF],
    0x115BB: [0x115B9, 0x115AF],
    0x11938: [0x11935, 0x11930],
    0x16121: [0x1611E, 0x1611E],
    0x16122: [0x1611E, 0x16129],
    0x16123: [0x1611E, 0x1611F],
    0x16124: [0x16129, 0x1611F],
    0x16125: [0x1611E, 0x16120],
    0x16126: [0x16121, 0x1611F],
    0x16127: [0x16122, 0x1611F],
    0x16128: [0x16121, 0x16120],
    0x16D68: [0x16D67, 0x16D67],
    0x16D69: [0x16D63, 0x16D67],
    0x16D6A: [0x16D69, 0x16D67],
    0x1CCD6: ["<font>", 0x0041],
    0x1CCD7: ["<font>", 0x0042],
    0x1CCD8: ["<font>", 0x0043],
    0x1CCD9: ["<font>", 0x0044],
    0x1CCDA: ["<font>", 0x0045],
    0x1CCDB: ["<font>", 0x0046],
    0x1CCDC: ["<font>", 0x0047],
    0x1CCDD: ["<font>", 0x0048],
    0x1CCDE: ["<font>", 0x0049],
    0x1CCDF: ["<font>", 0x004A],
    0x1CCE0: ["<font>", 0x004B],
    0x1CCE1: ["<font>", 0x004C],
    0x1CCE2: ["<font>", 0x004D],
    0x1CCE3: ["<font>", 0x004E],
    0x1CCE4: ["<font>", 0x004F],
    0x1CCE5: ["<font>", 0x0050],
    0x1CCE6: ["<font>", 0x0051],
    0x1CCE7: ["<font>", 0x0052],
    0x1CCE8: ["<font>", 0x0053],
    0x1CCE9: ["<font>", 0x0054],
    0x1CCEA: ["<font>", 0x0055],
    0x1CCEB: ["<font>", 0x0056],
    0x1CCEC: ["<font>", 0x0057],
    0x1CCED: ["<font>", 0x0058],
    0x1CCEE: ["<font>", 0x0059],
    0x1CCEF: ["<font>", 0x005A],
    0x1CCF0: ["<font>", 0x0030],
    0x1CCF1: ["<font>", 0x0031],
    0x1CCF2: ["<font>", 0x0032],
    0x1CCF3: ["<font>", 0x0033],
    0x1CCF4: ["<font>", 0x0034],
    0x1CCF5: ["<font>", 0x0035],
    0x1CCF6: ["<font>", 0x0036],
    0x1CCF7: ["<font>", 0x0037],
    0x1CCF8: ["<font>", 0x0038],
    0x1CCF9: ["<font>", 0x0039],
    0x1D15E: [0x1D157, 0x1D165],
    0x1D15F: [0x1D158, 0x1D165],
    0x1D160: [0x1D15F, 0x1D16E],
    0x1D161: [0x1D15F, 0x1D16F],
    0x1D162: [0x1D15F, 0x1D170],
    0x1D163: [0x1D15F, 0x1D171],
    0x1D164: [0x1D15F, 0x1D172],
    0x1D1BB: [0x1D1B9, 0x1D165],
    0x1D1BC: [0x1D1BA, 0x1D165],
    0x1D1BD: [0x1D1BB, 0x1D16E],
    0x1D1BE: [0x1D1BC, 0x1D16E],
    0x1D1BF: [0x1D1BB, 0x1D16F],
    0x1D1C0: [0x1D1BC, 0x1D16F],
    0x1D400: ["<font>", 0x0041],
    0x1D401: ["<font>", 0x0042],
    0x1D402: ["<font>", 0x0043],
    0x1D403: ["<font>", 0x0044],
    0x1D404: ["<font>", 0x0045],
    0x1D405: ["<font>", 0x0046],
    0x1D406: ["<font>", 0x0047],
    0x1D407: ["<font>", 0x0048],
    0x1D408: ["<font>", 0x0049],
    0x1D409: ["<font>", 0x004A],
    0x1D40A: ["<font>", 0x004B],
    0x1D40B: ["<font>", 0x004C],
    0x1D40C: ["<font>", 0x004D],
    0x1D40D: ["<font>", 0x004E],
    0x1D40E: ["<font>", 0x004F],
    0x1D40F: ["<font>", 0x0050],
    0x1D410: ["<font>", 0x0051],
    0x1D411: ["<font>", 0x0052],
    0x1D412: ["<font>", 0x0053],
    0x1D413: ["<font>", 0x0054],
    0x1D414: ["<font>", 0x0055],
    0x1D415: ["<font>", 0x0056],
    0x1D416: ["<font>", 0x0057],
    0x1D417: ["<font>", 0x0058],
    0x1D418: ["<font>", 0x0059],
    0x1D419: ["<font>", 0x005A],
    0x1D41A: ["<font>", 0x0061],
    0x1D41B: ["<font>", 0x0062],
    0x1D41C: ["<font>", 0x0063],
    0x1D41D: ["<font>", 0x0064],
    0x1D41E: ["<font>", 0x0065],
    0x1D41F: ["<font>", 0x0066],
    0x1D420: ["<font>", 0x0067],
    0x1D421: ["<font>", 0x0068],
    0x1D422: ["<font>", 0x0069],
    0x1D423: ["<font>", 0x006A],
    0x1D424: ["<font>", 0x006B],
    0x1D425: ["<font>", 0x006C],
    0x1D426: ["<font>", 0x006D],
    0x1D427: ["<font>", 0x006E],
    0x1D428: ["<font>", 0x006F],
    0x1D429: ["<font>", 0x0070],
    0x1D42A: ["<font>", 0x0071],
    0x1D42B: ["<font>", 0x0072],
    0x1D42C: ["<font>", 0x0073],
    0x1D42D: ["<font>", 0x0074],
    0x1D42E: ["<font>", 0x0075],
    0x1D42F: ["<font>", 0x0076],
    0x1D430: ["<font>", 0x0077],
    0x1D431: ["<font>", 0x0078],
    0x1D432: ["<font>", 0x0079],
    0x1D433: ["<font>", 0x007A],
    0x1D434: ["<font>", 0x0041],
    0x1D435: ["<font>", 0x0042],
    0x1D436: ["<font>", 0x0043],
    0x1D437: ["<font>", 0x0044],
    0x1D438: ["<font>", 0x0045],
    0x1D439: ["<font>", 0x0046],
    0x1D43A: ["<font>", 0x0047],
    0x1D43B: ["<font>", 0x0048],
    0x1D43C: ["<font>", 0x0049],
    0x1D43D: ["<font>", 0x004A],
    0x1D43E: ["<font>", 0x004B],
    0x1D43F: ["<font>", 0x004C],
    0x1D440: ["<font>", 0x004D],
    0x1D441: ["<font>", 0x004E],
    0x1D442: ["<font>", 0x004F],
    0x1D443: ["<font>", 0x0050],
    0x1D444: ["<font>", 0x0051],
    0x1D445: ["<font>", 0x0052],
    0x1D446: ["<font>", 0x0053],
    0x1D447: ["<font>", 0x0054],
    0x1D448: ["<font>", 0x0055],
    0x1D449: ["<font>", 0x0056],
    0x1D44A: ["<font>", 0x0057],
    0x1D44B: ["<font>", 0x0058],
    0x1D44C: ["<font>", 0x0059],
    0x1D44D: ["<font>", 0x005A],
    0x1D44E: ["<font>", 0x0061],
    0x1D44F: ["<font>", 0x0062],
    0x1D450: ["<font>", 0x0063],
    0x1D451: ["<font>", 0x0064],
    0x1D452: ["<font>", 0x0065],
    0x1D453: ["<font>", 0x0066],
    0x1D454: ["<font>", 0x0067],
    0x1D456: ["<font>", 0x0069],
    0x1D457: ["<font>", 0x006A],
    0x1D458: ["<font>", 0x006B],
    0x1D459: ["<font>", 0x006C],
    0x1D45A: ["<font>", 0x006D],
    0x1D45B: ["<font>", 0x006E],
    0x1D45C: ["<font>", 0x006F],
    0x1D45D: ["<font>", 0x0070],
    0x1D45E: ["<font>", 0x0071],
    0x1D45F: ["<font>", 0x0072],
    0x1D460: ["<font>", 0x0073],
    0x1D461: ["<font>", 0x0074],
    0x1D462: ["<font>", 0x0075],
    0x1D463: ["<font>", 0x0076],
    0x1D464: ["<font>", 0x0077],
    0x1D465: ["<font>", 0x0078],
    0x1D466: ["<font>", 0x0079],
    0x1D467: ["<font>", 0x007A],
    0x1D468: ["<font>", 0x0041],
    0x1D469: ["<font>", 0x0042],
    0x1D46A: ["<font>", 0x0043],
    0x1D46B: ["<font>", 0x0044],
    0x1D46C: ["<font>", 0x0045],
    0x1D46D: ["<font>", 0x0046],
    0x1D46E: ["<font>", 0x0047],
    0x1D46F: ["<font>", 0x0048],
    0x1D470: ["<font>", 0x0049],
    0x1D471: ["<font>", 0x004A],
    0x1D472: ["<font>", 0x004B],
    0x1D473: ["<font>", 0x004C],
    0x1D474: ["<font>", 0x004D],
    0x1D475: ["<font>", 0x004E],
    0x1D476: ["<font>", 0x004F],
    0x1D477: ["<font>", 0x0050],
    0x1D478: ["<font>", 0x0051],
    0x1D479: ["<font>", 0x0052],
    0x1D47A: ["<font>", 0x0053],
    0x1D47B: ["<font>", 0x0054],
    0x1D47C: ["<font>", 0x0055],
    0x1D47D: ["<font>", 0x0056],
    0x1D47E: ["<font>", 0x0057],
    0x1D47F: ["<font>", 0x0058],
    0x1D480: ["<font>", 0x0059],
    0x1D481: ["<font>", 0x005A],
    0x1D482: ["<font>", 0x0061],
    0x1D483: ["<font>", 0x0062],
    0x1D484: ["<font>", 0x0063],
    0x1D485: ["<font>", 0x0064],
    0x1D486: ["<font>", 0x0065],
    0x1D487: ["<font>", 0x0066],
    0x1D488: ["<font>", 0x0067],
    0x1D489: ["<font>", 0x0068],
    0x1D48A: ["<font>", 0x0069],
    0x1D48B: ["<font>", 0x006A],
    0x1D48C: ["<font>", 0x006B],
    0x1D48D: ["<font>", 0x006C],
    0x1D48E: ["<font>", 0x006D],
    0x1D48F: ["<font>", 0x006E],
    0x1D490: ["<font>", 0x006F],
    0x1D491: ["<font>", 0x0070],
    0x1D492: ["<font>", 0x0071],
    0x1D493: ["<font>", 0x0072],
    0x1D494: ["<font>", 0x0073],
    0x1D495: ["<font>", 0x0074],
    0x1D496: ["<font>", 0x0075],
    0x1D497: ["<font>", 0x0076],
    0x1D498: ["<font>", 0x0077],
    0x1D499: ["<font>", 0x0078],
    0x1D49A: ["<font>", 0x0079],
    0x1D49B: ["<font>", 0x007A],
    0x1D49C: ["<font>", 0x0041],
    0x1D49E: ["<font>", 0x0043],
    0x1D49F: ["<font>", 0x0044],
    0x1D4A2: ["<font>", 0x0047],
    0x1D4A5: ["<font>", 0x004A],
    0x1D4A6: ["<font>", 0x004B],
    0x1D4A9: ["<font>", 0x004E],
    0x1D4AA: ["<font>", 0x004F],
    0x1D4AB: ["<font>", 0x0050],
    0x1D4AC: ["<font>", 0x0051],
    0x1D4AE: ["<font>", 0x0053],
    0x1D4AF: ["<font>", 0x0054],
    0x1D4B0: ["<font>", 0x0055],
    0x1D4B1: ["<font>", 0x0056],
    0x1D4B2: ["<font>", 0x0057],
    0x1D4B3: ["<font>", 0x0058],
    0x1D4B4: ["<font>", 0x0059],
    0x1D4B5: ["<font>", 0x005A],
    0x1D4B6: ["<font>", 0x0061],
    0x1D4B7: ["<font>", 0x0062],
    0x1D4B8: ["<font>", 0x0063],
    0x1D4B9: ["<font>", 0x0064],
    0x1D4BB: ["<font>", 0x0066],
    0x1D4BD: ["<font>", 0x0068],
    0x1D4BE: ["<font>", 0x0069],
    0x1D4BF: ["<font>", 0x006A],
    0x1D4C0: ["<font>", 0x006B],
    0x1D4C1: ["<font>", 0x006C],
    0x1D4C2: ["<font>", 0x006D],
    0x1D4C3: ["<font>", 0x006E],
    0x1D4C5: ["<font>", 0x0070],
    0x1D4C6: ["<font>", 0x0071],
    0x1D4C7: ["<font>", 0x0072],
    0x1D4C8: ["<font>", 0x0073],
    0x1D4C9: ["<font>", 0x0074],
    0x1D4CA: ["<font>", 0x0075],
    0x1D4CB: ["<font>", 0x0076],
    0x1D4CC: ["<font>", 0x0077],
    0x1D4CD: ["<font>", 0x0078],
    0x1D4CE: ["<font>", 0x0079],
    0x1D4CF: ["<font>", 0x007A],
    0x1D4D0: ["<font>", 0x0041],
    0x1D4D1: ["<font>", 0x0042],
    0x1D4D2: ["<font>", 0x0043],
    0x1D4D3: ["<font>", 0x0044],
    0x1D4D4: ["<font>", 0x0045],
    0x1D4D5: ["<font>", 0x0046],
    0x1D4D6: ["<font>", 0x0047],
    0x1D4D7: ["<font>", 0x0048],
    0x1D4D8: ["<font>", 0x0049],
    0x1D4D9: ["<font>", 0x004A],
    0x1D4DA: ["<font>", 0x004B],
    0x1D4DB: ["<font>", 0x004C],
    0x1D4DC: ["<font>", 0x004D],
    0x1D4DD: ["<font>", 0x004E],
    0x1D4DE: ["<font>", 0x004F],
    0x1D4DF: ["<font>", 0x0050],
    0x1D4E0: ["<font>", 0x0051],
    0x1D4E1: ["<font>", 0x0052],
    0x1D4E2: ["<font>", 0x0053],
    0x1D4E3: ["<font>", 0x0054],
    0x1D4E4: ["<font>", 0x0055],
    0x1D4E5: ["<font>", 0x0056],
    0x1D4E6: ["<font>", 0x0057],
    0x1D4E7: ["<font>", 0x0058],
    0x1D4E8: ["<font>", 0x0059],
    0x1D4E9: ["<font>", 0x005A],
    0x1D4EA: ["<font>", 0x0061],
    0x1D4EB: ["<font>", 0x0062],
    0x1D4EC: ["<font>", 0x0063],
    0x1D4ED: ["<font>", 0x0064],
    0x1D4EE: ["<font>", 0x0065],
    0x1D4EF: ["<font>", 0x0066],
    0x1D4F0: ["<font>", 0x0067],
    0x1D4F1: ["<font>", 0x0068],
    0x1D4F2: ["<font>", 0x0069],
    0x1D4F3: ["<font>", 0x006A],
    0x1D4F4: ["<font>", 0x006B],
    0x1D4F5: ["<font>", 0x006C],
    0x1D4F6: ["<font>", 0x006D],
    0x1D4F7: ["<font>", 0x006E],
    0x1D4F8: ["<font>", 0x006F],
    0x1D4F9: ["<font>", 0x0070],
    0x1D4FA: ["<font>", 0x0071],
    0x1D4FB: ["<font>", 0x0072],
    0x1D4FC: ["<font>", 0x0073],
    0x1D4FD: ["<font>", 0x0074],
    0x1D4FE: ["<font>", 0x0075],
    0x1D4FF: ["<font>", 0x0076],
    0x1D500: ["<font>", 0x0077],
    0x1D501: ["<font>", 0x0078],
    0x1D502: ["<font>", 0x0079],
    0x1D503: ["<font>", 0x007A],
    0x1D504: ["<font>", 0x0041],
    0x1D505: ["<font>", 0x0042],
    0x1D507: ["<font>", 0x0044],
    0x1D508: ["<font>", 0x0045],
    0x1D509: ["<font>", 0x0046],
    0x1D50A: ["<font>", 0x0047],
    0x1D50D: ["<font>", 0x004A],
    0x1D50E: ["<font>", 0x004B],
    0x1D50F: ["<font>", 0x004C],
    0x1D510: ["<font>", 0x004D],
    0x1D511: ["<font>", 0x004E],
    0x1D512: ["<font>", 0x004F],
    0x1D513: ["<font>", 0x0050],
    0x1D514: ["<font>", 0x0051],
    0x1D516: ["<font>", 0x0053],
    0x1D517: ["<font>", 0x0054],
    0x1D518: ["<font>", 0x0055],
    0x1D519: ["<font>", 0x0056],
    0x1D51A: ["<font>", 0x0057],
    0x1D51B: ["<font>", 0x0058],
    0x1D51C: ["<font>", 0x0059],
    0x1D51E: ["<font>", 0x0061],
    0x1D51F: ["<font>", 0x0062],
    0x1D520: ["<font>", 0x0063],
    0x1D521: ["<font>", 0x0064],
    0x1D522: ["<font>", 0x0065],
    0x1D523: ["<font>", 0x0066],
    0x1D524: ["<font>", 0x0067],
    0x1D525: ["<font>", 0x0068],
    0x1D526: ["<font>", 0x0069],
    0x1D527: ["<font>", 0x006A],
    0x1D528: ["<font>", 0x006B],
    0x1D529: ["<font>", 0x006C],
    0x1D52A: ["<font>", 0x006D],
    0x1D52B: ["<font>", 0x006E],
    0x1D52C: ["<font>", 0x006F],
    0x1D52D: ["<font>", 0x0070],
    0x1D52E: ["<font>", 0x0071],
    0x1D52F: ["<font>", 0x0072],
    0x1D530: ["<font>", 0x0073],
    0x1D531: ["<font>", 0x0074],
    0x1D532: ["<font>", 0x0075],
    0x1D533: ["<font>", 0x0076],
    0x1D534: ["<font>", 0x0077],
    0x1D535: ["<font>", 0x0078],
    0x1D536: ["<font>", 0x0079],
    0x1D537: ["<font>", 0x007A],
    0x1D538: ["<font>", 0x0041],
    0x1D539: ["<font>", 0x0042],
    0x1D53B: ["<font>", 0x0044],
    0x1D53C: ["<font>", 0x0045],
    0x1D53D: ["<font>", 0x0046],
    0x1D53E: ["<font>", 0x0047],
    0x1D540: ["<font>", 0x0049],
    0x1D541: ["<font>", 0x004A],
    0x1D542: ["<font>", 0x004B],
    0x1D543: ["<font>", 0x004C],
    0x1D544: ["<font>", 0x004D],
    0x1D546: ["<font>", 0x004F],
    0x1D54A: ["<font>", 0x0053],
    0x1D54B: ["<font>", 0x0054],
    0x1D54C: ["<font>", 0x0055],
    0x1D54D: ["<font>", 0x0056],
    0x1D54E: ["<font>", 0x0057],
    0x1D54F: ["<font>", 0x0058],
    0x1D550: ["<font>", 0x0059],
    0x1D552: ["<font>", 0x0061],
    0x1D553: ["<font>", 0x0062],
    0x1D554: ["<font>", 0x0063],
    0x1D555: ["<font>", 0x0064],
    0x1D556: ["<font>", 0x0065],
    0x1D557: ["<font>", 0x0066],
    0x1D558: ["<font>", 0x0067],
    0x1D559: ["<font>", 0x0068],
    0x1D55A: ["<font>", 0x0069],
    0x1D55B: ["<font>", 0x006A],
    0x1D55C: ["<font>", 0x006B],
    0x1D55D: ["<font>", 0x006C],
    0x1D55E: ["<font>", 0x006D],
    0x1D55F: ["<font>", 0x006E],
    0x1D560: ["<font>", 0x006F],
    0x1D561: ["<font>", 0x0070],
    0x1D562: ["<font>", 0x0071],
    0x1D563: ["<font>", 0x0072],
    0x1D564: ["<font>", 0x0073],
    0x1D565: ["<font>", 0x0074],
    0x1D566: ["<font>", 0x0075],
    0x1D567: ["<font>", 0x0076],
    0x1D568: ["<font>", 0x0077],
    0x1D569: ["<font>", 0x0078],
    0x1D56A: ["<font>", 0x0079],
    0x1D56B: ["<font>", 0x007A],
    0x1D56C: ["<font>", 0x0041],
    0x1D56D: ["<font>", 0x0042],
    0x1D56E: ["<font>", 0x0043],
    0x1D56F: ["<font>", 0x0044],
    0x1D570: ["<font>", 0x0045],
    0x1D571: ["<font>", 0x0046],
    0x1D572: ["<font>", 0x0047],
    0x1D573: ["<font>", 0x0048],
    0x1D574: ["<font>", 0x0049],
    0x1D575: ["<font>", 0x004A],
    0x1D576: ["<font>", 0x004B],
    0x1D577: ["<font>", 0x004C],
    0x1D578: ["<font>", 0x004D],
    0x1D579: ["<font>", 0x004E],
    0x1D57A: ["<font>", 0x004F],
    0x1D57B: ["<font>", 0x0050],
    0x1D57C: ["<font>", 0x0051],
    0x1D57D: ["<font>", 0x0052],
    0x1D57E: ["<font>", 0x0053],
    0x1D57F: ["<font>", 0x0054],
    0x1D580: ["<font>", 0x0055],
    0x1D581: ["<font>", 0x0056],
    0x1D582: ["<font>", 0x0057],
    0x1D583: ["<font>", 0x0058],
    0x1D584: ["<font>", 0x0059],
    0x1D585: ["<font>", 0x005A],
    0x1D586: ["<font>", 0x0061],
    0x1D587: ["<font>", 0x0062],
    0x1D588: ["<font>", 0x0063],
    0x1D589: ["<font>", 0x0064],
    0x1D58A: ["<font>", 0x0065],
    0x1D58B: ["<font>", 0x0066],
    0x1D58C: ["<font>", 0x0067],
    0x1D58D: ["<font>", 0x0068],
    0x1D58E: ["<font>", 0x0069],
    0x1D58F: ["<font>", 0x006A],
    0x1D590: ["<font>", 0x006B],
    0x1D591: ["<font>", 0x006C],
    0x1D592: ["<font>", 0x006D],
    0x1D593: ["<font>", 0x006E],
    0x1D594: ["<font>", 0x006F],
    0x1D595: ["<font>", 0x0070],
    0x1D596: ["<font>", 0x0071],
    0x1D597: ["<font>", 0x0072],
    0x1D598: ["<font>", 0x0073],
    0x1D599: ["<font>", 0x0074],
    0x1D59A: ["<font>", 0x0075],
    0x1D59B: ["<font>", 0x0076],
    0x1D59C: ["<font>", 0x0077],
    0x1D59D: ["<font>", 0x0078],
    0x1D59E: ["<font>", 0x0079],
    0x1D59F: ["<font>", 0x007A],
    0x1D5A0: ["<font>", 0x0041],
    0x1D5A1: ["<font>", 0x0042],
    0x1D5A2: ["<font>", 0x0043],
    0x1D5A3: ["<font>", 0x0044],
    0x1D5A4: ["<font>", 0x0045],
    0x1D5A5: ["<font>", 0x0046],
    0x1D5A6: ["<font>", 0x0047],
    0x1D5A7: ["<font>", 0x0048],
    0x1D5A8: ["<font>", 0x0049],
    0x1D5A9: ["<font>", 0x004A],
    0x1D5AA: ["<font>", 0x004B],
    0x1D5AB: ["<font>", 0x004C],
    0x1D5AC: ["<font>", 0x004D],
    0x1D5AD: ["<font>", 0x004E],
    0x1D5AE: ["<font>", 0x004F],
    0x1D5AF: ["<font>", 0x0050],
    0x1D5B0: ["<font>", 0x0051],
    0x1D5B1: ["<font>", 0x0052],
    0x1D5B2: ["<font>", 0x0053],
    0x1D5B3: ["<font>", 0x0054],
    0x1D5B4: ["<font>", 0x0055],
    0x1D5B5: ["<font>", 0x0056],
    0x1D5B6: ["<font>", 0x0057],
    0x1D5B7: ["<font>", 0x0058],
    0x1D5B8: ["<font>", 0x0059],
    0x1D5B9: ["<font>", 0x005A],
    0x1D5BA: ["<font>", 0x0061],
    0x1D5BB: ["<font>", 0x0062],
    0x1D5BC: ["<font>", 0x0063],
    0x1D5BD: ["<font>", 0x0064],
    0x1D5BE: ["<font>", 0x0065],
    0x1D5BF: ["<font>", 0x0066],
    0x1D5C0: ["<font>", 0x0067],
    0x1D5C1: ["<font>", 0x0068],
    0x1D5C2: ["<font>", 0x0069],
    0x1D5C3: ["<font>", 0x006A],
    0x1D5C4: ["<font>", 0x006B],
    0x1D5C5: ["<font>", 0x006C],
    0x1D5C6: ["<font>", 0x006D],
    0x1D5C7: ["<font>", 0x006E],
    0x1D5C8: ["<font>", 0x006F],
    0x1D5C9: ["<font>", 0x0070],
    0x1D5CA: ["<font>", 0x0071],
    0x1D5CB: ["<font>", 0x0072],
    0x1D5CC: ["<font>", 0x0073],
    0x1D5CD: ["<font>", 0x0074],
    0x1D5CE: ["<font>", 0x0075],
    0x1D5CF: ["<font>", 0x0076],
    0x1D5D0: ["<font>", 0x0077],
    0x1D5D1: ["<font>", 0x0078],
    0x1D5D2: ["<font>", 0x0079],
    0x1D5D3: ["<font>", 0x007A],
    0x1D5D4: ["<font>", 0x0041],
    0x1D5D5: ["<font>", 0x0042],
    0x1D5D6: ["<font>", 0x0043],
    0x1D5D7: ["<font>", 0x0044],
    0x1D5D8: ["<font>", 0x0045],
    0x1D5D9: ["<font>", 0x0046],
    0x1D5DA: ["<font>", 0x0047],
    0x1D5DB: ["<font>", 0x0048],
    0x1D5DC: ["<font>", 0x0049],
    0x1D5DD: ["<font>", 0x004A],
    0x1D5DE: ["<font>", 0x004B],
    0x1D5DF: ["<font>", 0x004C],
    0x1D5E0: ["<font>", 0x004D],
    0x1D5E1: ["<font>", 0x004E],
    0x1D5E2: ["<font>", 0x004F],
    0x1D5E3: ["<font>", 0x0050],
    0x1D5E4: ["<font>", 0x0051],
    0x1D5E5: ["<font>", 0x0052],
    0x1D5E6: ["<font>", 0x0053],
    0x1D5E7: ["<font>", 0x0054],
    0x1D5E8: ["<font>", 0x0055],
    0x1D5E9: ["<font>", 0x0056],
    0x1D5EA: ["<font>", 0x0057],
    0x1D5EB: ["<font>", 0x0058],
    0x1D5EC: ["<font>", 0x0059],
    0x1D5ED: ["<font>", 0x005A],
    0x1D5EE: ["<font>", 0x0061],
    0x1D5EF: ["<font>", 0x0062],
    0x1D5F0: ["<font>", 0x0063],
    0x1D5F1: ["<font>", 0x0064],
    0x1D5F2: ["<font>", 0x0065],
    0x1D5F3: ["<font>", 0x0066],
    0x1D5F4: ["<font>", 0x0067],
    0x1D5F5: ["<font>", 0x0068],
    0x1D5F6: ["<font>", 0x0069],
    0x1D5F7: ["<font>", 0x006A],
    0x1D5F8: ["<font>", 0x006B],
    0x1D5F9: ["<font>", 0x006C],
    0x1D5FA: ["<font>", 0x006D],
    0x1D5FB: ["<font>", 0x006E],
    0x1D5FC: ["<font>", 0x006F],
    0x1D5FD: ["<font>", 0x0070],
    0x1D5FE: ["<font>", 0x0071],
    0x1D5FF: ["<font>", 0x0072],
    0x1D600: ["<font>", 0x0073],
    0x1D601: ["<font>", 0x0074],
    0x1D602: ["<font>", 0x0075],
    0x1D603: ["<font>", 0x0076],
    0x1D604: ["<font>", 0x0077],
    0x1D605: ["<font>", 0x0078],
    0x1D606: ["<font>", 0x0079],
    0x1D607: ["<font>", 0x007A],
    0x1D608: ["<font>", 0x0041],
    0x1D609: ["<font>", 0x0042],
    0x1D60A: ["<font>", 0x0043],
    0x1D60B: ["<font>", 0x0044],
    0x1D60C: ["<font>", 0x0045],
    0x1D60D: ["<font>", 0x0046],
    0x1D60E: ["<font>", 0x0047],
    0x1D60F: ["<font>", 0x0048],
    0x1D610: ["<font>", 0x0049],
    0x1D611: ["<font>", 0x004A],
    0x1D612: ["<font>", 0x004B],
    0x1D613: ["<font>", 0x004C],
    0x1D614: ["<font>", 0x004D],
    0x1D615: ["<font>", 0x004E],
    0x1D616: ["<font>", 0x004F],
    0x1D617: ["<font>", 0x0050],
    0x1D618: ["<font>", 0x0051],
    0x1D619: ["<font>", 0x0052],
    0x1D61A: ["<font>", 0x0053],
    0x1D61B: ["<font>", 0x0054],
    0x1D61C: ["<font>", 0x0055],
    0x1D61D: ["<font>", 0x0056],
    0x1D61E: ["<font>", 0x0057],
    0x1D61F: ["<font>", 0x0058],
    0x1D620: ["<font>", 0x0059],
    0x1D621: ["<font>", 0x005A],
    0x1D622: ["<font>", 0x0061],
    0x1D623: ["<font>", 0x0062],
    0x1D624: ["<font>", 0x0063],
    0x1D625: ["<font>", 0x0064],
    0x1D626: ["<font>", 0x0065],
    0x1D627: ["<font>", 0x0066],
    0x1D628: ["<font>", 0x0067],
    0x1D629: ["<font>", 0x0068],
    0x1D62A: ["<font>", 0x0069],
    0x1D62B: ["<font>", 0x006A],
    0x1D62C: ["<font>", 0x006B],
    0x1D62D: ["<font>", 0x006C],
    0x1D62E: ["<font>", 0x006D],
    0x1D62F: ["<font>", 0x006E],
    0x1D630: ["<font>", 0x006F],
    0x1D631: ["<font>", 0x0070],
    0x1D632: ["<font>", 0x0071],
    0x1D633: ["<font>", 0x0072],
    0x1D634: ["<font>", 0x0073],
    0x1D635: ["<font>", 0x0074],
    0x1D636: ["<font>", 0x0075],
    0x1D637: ["<font>", 0x0076],
    0x1D638: ["<font>", 0x0077],
    0x1D639: ["<font>", 0x0078],
    0x1D63A: ["<font>", 0x0079],
    0x1D63B: ["<font>", 0x007A],
    0x1D63C: ["<font>", 0x0041],
    0x1D63D: ["<font>", 0x0042],
    0x1D63E: ["<font>", 0x0043],
    0x1D63F: ["<font>", 0x0044],
    0x1D640: ["<font>", 0x0045],
    0x1D641: ["<font>", 0x0046],
    0x1D642: ["<font>", 0x0047],
    0x1D643: ["<font>", 0x0048],
    0x1D644: ["<font>", 0x0049],
    0x1D645: ["<font>", 0x004A],
    0x1D646: ["<font>", 0x004B],
    0x1D647: ["<font>", 0x004C],
    0x1D648: ["<font>", 0x004D],
    0x1D649: ["<font>", 0x004E],
    0x1D64A: ["<font>", 0x004F],
    0x1D64B: ["<font>", 0x0050],
    0x1D64C: ["<font>", 0x0051],
    0x1D64D: ["<font>", 0x0052],
    0x1D64E: ["<font>", 0x0053],
    0x1D64F: ["<font>", 0x0054],
    0x1D650: ["<font>", 0x0055],
    0x1D651: ["<font>", 0x0056],
    0x1D652: ["<font>", 0x0057],
    0x1D653: ["<font>", 0x0058],
    0x1D654: ["<font>", 0x0059],
    0x1D655: ["<font>", 0x005A],
    0x1D656: ["<font>", 0x0061],
    0x1D657: ["<font>", 0x0062],
    0x1D658: ["<font>", 0x0063],
    0x1D659: ["<font>", 0x0064],
    0x1D65A: ["<font>", 0x0065],
    0x1D65B: ["<font>", 0x0066],
    0x1D65C: ["<font>", 0x0067],
    0x1D65D: ["<font>", 0x0068],
    0x1D65E: ["<font>", 0x0069],
    0x1D65F: ["<font>", 0x006A],
    0x1D660: ["<font>", 0x006B],
    0x1D661: ["<font>", 0x006C],
    0x1D662: ["<font>", 0x006D],
    0x1D663: ["<font>", 0x006E],
    0x1D664: ["<font>", 0x006F],
    0x1D665: ["<font>", 0x0070],
    0x1D666: ["<font>", 0x0071],
    0x1D667: ["<font>", 0x0072],
    0x1D668: ["<font>", 0x0073],
    0x1D669: ["<font>", 0x0074],
    0x1D66A: ["<font>", 0x0075],
    0x1D66B: ["<font>", 0x0076],
    0x1D66C: ["<font>", 0x0077],
    0x1D66D: ["<font>", 0x0078],
    0x1D66E: ["<font>", 0x0079],
    0x1D66F: ["<font>", 0x007A],
    0x1D670: ["<font>", 0x0041],
    0x1D671: ["<font>", 0x0042],
    0x1D672: ["<font>", 0x0043],
    0x1D673: ["<font>", 0x0044],
    0x1D674: ["<font>", 0x0045],
    0x1D675: ["<font>", 0x0046],
    0x1D676: ["<font>", 0x0047],
    0x1D677: ["<font>", 0x0048],
    0x1D678: ["<font>", 0x0049],
    0x1D679: ["<font>", 0x004A],
    0x1D67A: ["<font>", 0x004B],
    0x1D67B: ["<font>", 0x004C],
    0x1D67C: ["<font>", 0x004D],
    0x1D67D: ["<font>", 0x004E],
    0x1D67E: ["<font>", 0x004F],
    0x1D67F: ["<font>", 0x0050],
    0x1D680: ["<font>", 0x0051],
    0x1D681: ["<font>", 0x0052],
    0x1D682: ["<font>", 0x0053],
    0x1D683: ["<font>", 0x0054],
    0x1D684: ["<font>", 0x0055],
    0x1D685: ["<font>", 0x0056],
    0x1D686: ["<font>", 0x0057],
    0x1D687: ["<font>", 0x0058],
    0x1D688: ["<font>", 0x0059],
    0x1D689: ["<font>", 0x005A],
    0x1D68A: ["<font>", 0x0061],
    0x1D68B: ["<font>", 0x0062],
    0x1D68C: ["<font>", 0x0063],
    0x1D68D: ["<font>", 0x0064],
    0x1D68E: ["<font>", 0x0065],
    0x1D68F: ["<font>", 0x0066],
    0x1D690: ["<font>", 0x0067],
    0x1D691: ["<font>", 0x0068],
    0x1D692: ["<font>", 0x0069],
    0x1D693: ["<font>", 0x006A],
    0x1D694: ["<font>", 0x006B],
    0x1D695: ["<font>", 0x006C],
    0x1D696: ["<font>", 0x006D],
    0x1D697: ["<font>", 0x006E],
    0x1D698: ["<font>", 0x006F],
    0x1D699: ["<font>", 0x0070],
    0x1D69A: ["<font>", 0x0071],
    0x1D69B: ["<font>", 0x0072],
    0x1D69C: ["<font>", 0x0073],
    0x1D69D: ["<font>", 0x0074],
    0x1D69E: ["<font>", 0x0075],
    0x1D69F: ["<font>", 0x0076],
    0x1D6A0: ["<font>", 0x0077],
    0x1D6A1: ["<font>", 0x0078],
    0x1D6A2: ["<font>", 0x0079],
    0x1D6A3: ["<font>", 0x007A],
    0x1D6A4: ["<font>", 0x0131],
    0x1D6A5: ["<font>", 0x0237],
    0x1D6A8: ["<font>", 0x0391],
    0x1D6A9: ["<font>", 0x0392],
    0x1D6AA: ["<font>", 0x0393],
    0x1D6AB: ["<font>", 0x0394],
    0x1D6AC: ["<font>", 0x0395],
    0x1D6AD: ["<font>", 0x0396],
    0x1D6AE: ["<font>", 0x0397],
    0x1D6AF: ["<font>", 0x0398],
    0x1D6B0: ["<font>", 0x0399],
    0x1D6B1: ["<font>", 0x039A],
    0x1D6B2: ["<font>", 0x039B],
    0x1D6B3: ["<font>", 0x039C],
    0x1D6B4: ["<font>", 0x039D],
    0x1D6B5: ["<font>", 0x039E],
    0x1D6B6: ["<font>", 0x039F],
    0x1D6B7: ["<font>", 0x03A0],
    0x1D6B8: ["<font>", 0x03A1],
    0x1D6B9: ["<font>", 0x03F4],
    0x1D6BA: ["<font>", 0x03A3],
    0x1D6BB: ["<font>", 0x03A4],
    0x1D6BC: ["<font>", 0x03A5],
    0x1D6BD: ["<font>", 0x03A6],
    0x1D6BE: ["<font>", 0x03A7],
    0x1D6BF: ["<font>", 0x03A8],
    0x1D6C0: ["<font>", 0x03A9],
    0x1D6C1: ["<font>", 0x2207],
    0x1D6C2: ["<font>", 0x03B1],
    0x1D6C3: ["<font>", 0x03B2],
    0x1D6C4: ["<font>", 0x03B3],
    0x1D6C5: ["<font>", 0x03B4],
    0x1D6C6: ["<font>", 0x03B5],
    0x1D6C7: ["<font>", 0x03B6],
    0x1D6C8: ["<font>", 0x03B7],
    0x1D6C9: ["<font>", 0x03B8],
    0x1D6CA: ["<font>", 0x03B9],
    0x1D6CB: ["<font>", 0x03BA],
    0x1D6CC: ["<font>", 0x03BB],
    0x1D6CD: ["<font>", 0x03BC],
    0x1D6CE: ["<font>", 0x03BD],
    0x1D6CF: ["<font>", 0x03BE],
    0x1D6D0: ["<font>", 0x03BF],
    0x1D6D1: ["<font>", 0x03C0],
    0x1D6D2: ["<font>", 0x03C1],
    0x1D6D3: ["<font>", 0x03C2],
    0x1D6D4: ["<font>", 0x03C3],
    0x1D6D5: ["<font>", 0x03C4],
    0x1D6D6: ["<font>", 0x03C5],
    0x1D6D7: ["<font>", 0x03C6],
    0x1D6D8: ["<font>", 0x03C7],
    0x1D6D9: ["<font>", 0x03C8],
    0x1D6DA: ["<font>", 0x03C9],
    0x1D6DB: ["<font>", 0x2202],
    0x1D6DC: ["<font>", 0x03F5],
    0x1D6DD: ["<font>", 0x03D1],
    0x1D6DE: ["<font>", 0x03F0],
    0x1D6DF: ["<font>", 0x03D5],
    0x1D6E0: ["<font>", 0x03F1],
    0x1D6E1: ["<font>", 0x03D6],
    0x1D6E2: ["<font>", 0x0391],
    0x1D6E3: ["<font>", 0x0392],
    0x1D6E4: ["<font>", 0x0393],
    0x1D6E5: ["<font>", 0x0394],
    0x1D6E6: ["<font>", 0x0395],
    0x1D6E7: ["<font>", 0x0396],
    0x1D6E8: ["<font>", 0x0397],
    0x1D6E9: ["<font>", 0x0398],
    0x1D6EA: ["<font>", 0x0399],
    0x1D6EB: ["<font>", 0x039A],
    0x1D6EC: ["<font>", 0x039B],
    0x1D6ED: ["<font>", 0x039C],
    0x1D6EE: ["<font>", 0x039D],
    0x1D6EF: ["<font>", 0x039E],
    0x1D6F0: ["<font>", 0x039F],
    0x1D6F1: ["<font>", 0x03A0],
    0x1D6F2: ["<font>", 0x03A1],
    0x1D6F3: ["<font>", 0x03F4],
    0x1D6F4: ["<font>", 0x03A3],
    0x1D6F5: ["<font>", 0x03A4],
    0x1D6F6: ["<font>", 0x03A5],
    0x1D6F7: ["<font>", 0x03A6],
    0x1D6F8: ["<font>", 0x03A7],
    0x1D6F9: ["<font>", 0x03A8],
    0x1D6FA: ["<font>", 0x03A9],
    0x1D6FB: ["<font>", 0x2207],
    0x1D6FC: ["<font>", 0x03B1],
    0x1D6FD: ["<font>", 0x03B2],
    0x1D6FE: ["<font>", 0x03B3],
    0x1D6FF: ["<font>", 0x03B4],
    0x1D700: ["<font>", 0x03B5],
    0x1D701: ["<font>", 0x03B6],
    0x1D702: ["<font>", 0x03B7],
    0x1D703: ["<font>", 0x03B8],
    0x1D704: ["<font>", 0x03B9],
    0x1D705: ["<font>", 0x03BA],
    0x1D706: ["<font>", 0x03BB],
    0x1D707: ["<font>", 0x03BC],
    0x1D708: ["<font>", 0x03BD],
    0x1D709: ["<font>", 0x03BE],
    0x1D70A: ["<font>", 0x03BF],
    0x1D70B: ["<font>", 0x03C0],
    0x1D70C: ["<font>", 0x03C1],
    0x1D70D: ["<font>", 0x03C2],
    0x1D70E: ["<font>", 0x03C3],
    0x1D70F: ["<font>", 0x03C4],
    0x1D710: ["<font>", 0x03C5],
    0x1D711: ["<font>", 0x03C6],
    0x1D712: ["<font>", 0x03C7],
    0x1D713: ["<font>", 0x03C8],
    0x1D714: ["<font>", 0x03C9],
    0x1D715: ["<font>", 0x2202],
    0x1D716: ["<font>", 0x03F5],
    0x1D717: ["<font>", 0x03D1],
    0x1D718: ["<font>", 0x03F0],
    0x1D719: ["<font>", 0x03D5],
    0x1D71A: ["<font>", 0x03F1],
    0x1D71B: ["<font>", 0x03D6],
    0x1D71C: ["<font>", 0x0391],
    0x1D71D: ["<font>", 0x0392],
    0x1D71E: ["<font>", 0x0393],
    0x1D71F: ["<font>", 0x0394],
    0x1D720: ["<font>", 0x0395],
    0x1D721: ["<font>", 0x0396],
    0x1D722: ["<font>", 0x0397],
    0x1D723: ["<font>", 0x0398],
    0x1D724: ["<font>", 0x0399],
    0x1D725: ["<font>", 0x039A],
    0x1D726: ["<font>", 0x039B],
    0x1D727: ["<font>", 0x039C],
    0x1D728: ["<font>", 0x039D],
    0x1D729: ["<font>", 0x039E],
    0x1D72A: ["<font>", 0x039F],
    0x1D72B: ["<font>", 0x03A0],
    0x1D72C: ["<font>", 0x03A1],
    0x1D72D: ["<font>", 0x03F4],
    0x1D72E: ["<font>", 0x03A3],
    0x1D72F: ["<font>", 0x03A4],
    0x1D730: ["<font>", 0x03A5],
    0x1D731: ["<font>", 0x03A6],
    0x1D732: ["<font>", 0x03A7],
    0x1D733: ["<font>", 0x03A8],
    0x1D734: ["<font>", 0x03A9],
    0x1D735: ["<font>", 0x2207],
    0x1D736: ["<font>", 0x03B1],
    0x1D737: ["<font>", 0x03B2],
    0x1D738: ["<font>", 0x03B3],
    0x1D739: ["<font>", 0x03B4],
    0x1D73A: ["<font>", 0x03B5],
    0x1D73B: ["<font>", 0x03B6],
    0x1D73C: ["<font>", 0x03B7],
    0x1D73D: ["<font>", 0x03B8],
    0x1D73E: ["<font>", 0x03B9],
    0x1D73F: ["<font>", 0x03BA],
    0x1D740: ["<font>", 0x03BB],
    0x1D741: ["<font>", 0x03BC],
    0x1D742: ["<font>", 0x03BD],
    0x1D743: ["<font>", 0x03BE],
    0x1D744: ["<font>", 0x03BF],
    0x1D745: ["<font>", 0x03C0],
    0x1D746: ["<font>", 0x03C1],
    0x1D747: ["<font>", 0x03C2],
    0x1D748: ["<font>", 0x03C3],
    0x1D749: ["<font>", 0x03C4],
    0x1D74A: ["<font>", 0x03C5],
    0x1D74B: ["<font>", 0x03C6],
    0x1D74C: ["<font>", 0x03C7],
    0x1D74D: ["<font>", 0x03C8],
    0x1D74E: ["<font>", 0x03C9],
    0x1D74F: ["<font>", 0x2202],
    0x1D750: ["<font>", 0x03F5],
    0x1D751: ["<font>", 0x03D1],
    0x1D752: ["<font>", 0x03F0],
    0x1D753: ["<font>", 0x03D5],
    0x1D754: ["<font>", 0x03F1],
    0x1D755: ["<font>", 0x03D6],
    0x1D756: ["<font>", 0x0391],
    0x1D757: ["<font>", 0x0392],
    0x1D758: ["<font>", 0x0393],
    0x1D759: ["<font>", 0x0394],
    0x1D75A: ["<font>", 0x0395],
    0x1D75B: ["<font>", 0x0396],
    0x1D75C: ["<font>", 0x0397],
    0x1D75D: ["<font>", 0x0398],
    0x1D75E: ["<font>", 0x0399],
    0x1D75F: ["<font>", 0x039A],
    0x1D760: ["<font>", 0x039B],
    0x1D761: ["<font>", 0x039C],
    0x1D762: ["<font>", 0x039D],
    0x1D763: ["<font>", 0x039E],
    0x1D764: ["<font>", 0x039F],
    0x1D765: ["<font>", 0x03A0],
    0x1D766: ["<font>", 0x03A1],
    0x1D767: ["<font>", 0x03F4],
    0x1D768: ["<font>", 0x03A3],
    0x1D769: ["<font>", 0x03A4],
    0x1D76A: ["<font>", 0x03A5],
    0x1D76B: ["<font>", 0x03A6],
    0x1D76C: ["<font>", 0x03A7],
    0x1D76D: ["<font>", 0x03A8],
    0x1D76E: ["<font>", 0x03A9],
    0x1D76F: ["<font>", 0x2207],
    0x1D770: ["<font>", 0x03B1],
    0x1D771: ["<font>", 0x03B2],
    0x1D772: ["<font>", 0x03B3],
    0x1D773: ["<font>", 0x03B4],
    0x1D774: ["<font>", 0x03B5],
    0x1D775: ["<font>", 0x03B6],
    0x1D776: ["<font>", 0x03B7],
    0x1D777: ["<font>", 0x03B8],
    0x1D778: ["<font>", 0x03B9],
    0x1D779: ["<font>", 0x03BA],
    0x1D77A: ["<font>", 0x03BB],
    0x1D77B: ["<font>", 0x03BC],
    0x1D77C: ["<font>", 0x03BD],
    0x1D77D: ["<font>", 0x03BE],
    0x1D77E: ["<font>", 0x03BF],
    0x1D77F: ["<font>", 0x03C0],
    0x1D780: ["<font>", 0x03C1],
    0x1D781: ["<font>", 0x03C2],
    0x1D782: ["<font>", 0x03C3],
    0x1D783: ["<font>", 0x03C4],
    0x1D784: ["<font>", 0x03C5],
    0x1D785: ["<font>", 0x03C6],
    0x1D786: ["<font>", 0x03C7],
    0x1D787: ["<font>", 0x03C8],
    0x1D788: ["<font>", 0x03C9],
    0x1D789: ["<font>", 0x2202],
    0x1D78A: ["<font>", 0x03F5],
    0x1D78B: ["<font>", 0x03D1],
    0x1D78C: ["<font>", 0x03F0],
    0x1D78D: ["<font>", 0x03D5],
    0x1D78E: ["<font>", 0x03F1],
    0x1D78F: ["<font>", 0x03D6],
    0x1D790: ["<font>", 0x0391],
    0x1D791: ["<font>", 0x0392],
    0x1D792: ["<font>", 0x0393],
    0x1D793: ["<font>", 0x0394],
    0x1D794: ["<font>", 0x0395],
    0x1D795: ["<font>", 0x0396],
    0x1D796: ["<font>", 0x0397],
    0x1D797: ["<font>", 0x0398],
    0x1D798: ["<font>", 0x0399],
    0x1D799: ["<font>", 0x039A],
    0x1D79A: ["<font>", 0x039B],
    0x1D79B: ["<font>", 0x039C],
    0x1D79C: ["<font>", 0x039D],
    0x1D79D: ["<font>", 0x039E],
    0x1D79E: ["<font>", 0x039F],
    0x1D79F: ["<font>", 0x03A0],
    0x1D7A0: ["<font>", 0x03A1],
    0x1D7A1: ["<font>", 0x03F4],
    0x1D7A2: ["<font>", 0x03A3],
    0x1D7A3: ["<font>", 0x03A4],
    0x1D7A4: ["<font>", 0x03A5],
    0x1D7A5: ["<font>", 0x03A6],
    0x1D7A6: ["<font>", 0x03A7],
    0x1D7A7: ["<font>", 0x03A8],
    0x1D7A8: ["<font>", 0x03A9],
    0x1D7A9: ["<font>", 0x2207],
    0x1D7AA: ["<font>", 0x03B1],
    0x1D7AB: ["<font>", 0x03B2],
    0x1D7AC: ["<font>", 0x03B3],
    0x1D7AD: ["<font>", 0x03B4],
    0x1D7AE: ["<font>", 0x03B5],
    0x1D7AF: ["<font>", 0x03B6],
    0x1D7B0: ["<font>", 0x03B7],
    0x1D7B1: ["<font>", 0x03B8],
    0x1D7B2: ["<font>", 0x03B9],
    0x1D7B3: ["<font>", 0x03BA],
    0x1D7B4: ["<font>", 0x03BB],
    0x1D7B5: ["<font>", 0x03BC],
    0x1D7B6: ["<font>", 0x03BD],
    0x1D7B7: ["<font>", 0x03BE],
    0x1D7B8: ["<font>", 0x03BF],
    0x1D7B9: ["<font>", 0x03C0],
    0x1D7BA: ["<font>", 0x03C1],
    0x1D7BB: ["<font>", 0x03C2],
    0x1D7BC: ["<font>", 0x03C3],
    0x1D7BD: ["<font>", 0x03C4],
    0x1D7BE: ["<font>", 0x03C5],
    0x1D7BF: ["<font>", 0x03C6],
    0x1D7C0: ["<font>", 0x03C7],
    0x1D7C1: ["<font>", 0x03C8],
    0x1D7C2: ["<font>", 0x03C9],
    0x1D7C3: ["<font>", 0x2202],
    0x1D7C4: ["<font>", 0x03F5],
    0x1D7C5: ["<font>", 0x03D1],
    0x1D7C6: ["<font>", 0x03F0],
    0x1D7C7: ["<font>", 0x03D5],
    0x1D7C8: ["<font>", 0x03F1],
    0x1D7C9: ["<font>", 0x03D6],
    0x1D7CA: ["<font>", 0x03DC],
    0x1D7CB: ["<font>", 0x03DD],
    0x1D7CE: ["<font>", 0x0030],
    0x1D7CF: ["<font>", 0x0031],
    0x1D7D0: ["<font>", 0x0032],
    0x1D7D1: ["<font>", 0x0033],
    0x1D7D2: ["<font>", 0x0034],
    0x1D7D3: ["<font>", 0x0035],
    0x1D7D4: ["<font>", 0x0036],
    0x1D7D5: ["<font>", 0x0037],
    0x1D7D6: ["<font>", 0x0038],
    0x1D7D7: ["<font>", 0x0039],
    0x1D7D8: ["<font>", 0x0030],
    0x1D7D9: ["<font>", 0x0031],
    0x1D7DA: ["<font>", 0x0032],
    0x1D7DB: ["<font>", 0x0033],
    0x1D7DC: ["<font>", 0x0034],
    0x1D7DD: ["<font>", 0x0035],
    0x1D7DE: ["<font>", 0x0036],
    0x1D7DF: ["<font>", 0x0037],
    0x1D7E0: ["<font>", 0x0038],
    0x1D7E1: ["<font>", 0x0039],
    0x1D7E2: ["<font>", 0x0030],
    0x1D7E3: ["<font>", 0x0031],
    0x1D7E4: ["<font>", 0x0032],
    0x1D7E5: ["<font>", 0x0033],
    0x1D7E6: ["<font>", 0x0034],
    0x1D7E7: ["<font>", 0x0035],
    0x1D7E8: ["<font>", 0x0036],
    0x1D7E9: ["<font>", 0x0037],
    0x1D7EA: ["<font>", 0x0038],
    0x1D7EB: ["<font>", 0x0039],
    0x1D7EC: ["<font>", 0x0030],
    0x1D7ED: ["<font>", 0x0031],
    0x1D7EE: ["<font>", 0x0032],
    0x1D7EF: ["<font>", 0x0033],
    0x1D7F0: ["<font>", 0x0034],
    0x1D7F1: ["<font>", 0x0035],
    0x1D7F2: ["<font>", 0x0036],
    0x1D7F3: ["<font>", 0x0037],
    0x1D7F4: ["<font>", 0x0038],
    0x1D7F5: ["<font>", 0x0039],
    0x1D7F6: ["<font>", 0x0030],
    0x1D7F7: ["<font>", 0x0031],
    0x1D7F8: ["<font>", 0x0032],
    0x1D7F9: ["<font>", 0x0033],
    0x1D7FA: ["<font>", 0x0034],
    0x1D7FB: ["<font>", 0x0035],
    0x1D7FC: ["<font>", 0x0036],
    0x1D7FD: ["<font>", 0x0037],
    0x1D7FE: ["<font>", 0x0038],
    0x1D7FF: ["<font>", 0x0039],
    0x1E030: ["<super>", 0x0430],
    0x1E031: ["<super>", 0x0431],
    0x1E032: ["<super>", 0x0432],
    0x1E033: ["<super>", 0x0433],
    0x1E034: ["<super>", 0x0434],
    0x1E035: ["<super>", 0x0435],
    0x1E036: ["<super>", 0x0436],
    0x1E037: ["<super>", 0x0437],
    0x1E038: ["<super>", 0x0438],
    0x1E039: ["<super>", 0x043A],
    0x1E03A: ["<super>", 0x043B],
    0x1E03B: ["<super>", 0x043C],
    0x1E03C: ["<super>", 0x043E],
    0x1E03D: ["<super>", 0x043F],
    0x1E03E: ["<super>", 0x0440],
    0x1E03F: ["<super>", 0x0441],
    0x1E040: ["<super>", 0x0442],
    0x1E041: ["<super>", 0x0443],
    0x1E042: ["<super>", 0x0444],
    0x1E043: ["<super>", 0x0445],
    0x1E044: ["<super>", 0x0446],
    0x1E045: ["<super>", 0x0447],
    0x1E046: ["<super>", 0x0448],
    0x1E047: ["<super>", 0x044B],
    0x1E048: ["<super>", 0x044D],
    0x1E049: ["<super>", 0x044E],
    0x1E04A: ["<super>", 0xA689],
    0x1E04B: ["<super>", 0x04D9],
    0x1E04C: ["<super>", 0x0456],
    0x1E04D: ["<super>", 0x0458],
    0x1E04E: ["<super>", 0x04E9],
    0x1E04F: ["<super>", 0x04AF],
    0x1E050: ["<super>", 0x04CF],
    0x1E051: ["<sub>", 0x0430],
    0x1E052: ["<sub>", 0x0431],
    0x1E053: ["<sub>", 0x0432],
    0x1E054: ["<sub>", 0x0433],
    0x1E055: ["<sub>", 0x0434],
    0x1E056: ["<sub>", 0x0435],
    0x1E057: ["<sub>", 0x0436],
    0x1E058: ["<sub>", 0x0437],
    0x1E059: ["<sub>", 0x0438],
    0x1E05A: ["<sub>", 0x043A],
    0x1E05B: ["<sub>", 0x043B],
    0x1E05C: ["<sub>", 0x043E],
    0x1E05D: ["<sub>", 0x043F],
    0x1E05E: ["<sub>", 0x0441],
    0x1E05F: ["<sub>", 0x0443],
    0x1E060: ["<sub>", 0x0444],
    0x1E061: ["<sub>", 0x0445],
    0x1E062: ["<sub>", 0x0446],
    0x1E063: ["<sub>", 0x0447],
    0x1E064: ["<sub>", 0x0448],
    0x1E065: ["<sub>", 0x044A],
    0x1E066: ["<sub>", 0x044B],
    0x1E067: ["<sub>", 0x0491],
    0x1E068: ["<sub>", 0x0456],
    0x1E069: ["<sub>", 0x0455],
    0x1E06A: ["<sub>", 0x045F],
    0x1E06B: ["<super>", 0x04AB],
    0x1E06C: ["<super>", 0xA651],
    0x1E06D: ["<super>", 0x04B1],
    0x1EE00: ["<font>", 0x0627],
    0x1EE01: ["<font>", 0x0628],
    0x1EE02: ["<font>", 0x062C],
    0x1EE03: ["<font>", 0x062F],
    0x1EE05: ["<font>", 0x0648],
    0x1EE06: ["<font>", 0x0632],
    0x1EE07: ["<font>", 0x062D],
    0x1EE08: ["<font>", 0x0637],
    0x1EE09: ["<font>", 0x064A],
    0x1EE0A: ["<font>", 0x0643],
    0x1EE0B: ["<font>", 0x0644],
    0x1EE0C: ["<font>", 0x0645],
    0x1EE0D: ["<font>", 0x0646],
    0x1EE0E: ["<font>", 0x0633],
    0x1EE0F: ["<font>", 0x0639],
    0x1EE10: ["<font>", 0x0641],
    0x1EE11: ["<font>", 0x0635],
    0x1EE12: ["<font>", 0x0642],
    0x1EE13: ["<font>", 0x0631],
    0x1EE14: ["<font>", 0x0634],
    0x1EE15: ["<font>", 0x062A],
    0x1EE16: ["<font>", 0x062B],
    0x1EE17: ["<font>", 0x062E],
    0x1EE18: ["<font>", 0x0630],
    0x1EE19: ["<font>", 0x0636],
    0x1EE1A: ["<font>", 0x0638],
    0x1EE1B: ["<font>", 0x063A],
    0x1EE1C: ["<font>", 0x066E],
    0x1EE1D: ["<font>", 0x06BA],
    0x1EE1E: ["<font>", 0x06A1],
    0x1EE1F: ["<font>", 0x066F],
    0x1EE21: ["<font>", 0x0628],
    0x1EE22: ["<font>", 0x062C],
    0x1EE24: ["<font>", 0x0647],
    0x1EE27: ["<font>", 0x062D],
    0x1EE29: ["<font>", 0x064A],
    0x1EE2A: ["<font>", 0x0643],
    0x1EE2B: ["<font>", 0x0644],
    0x1EE2C: ["<font>", 0x0645],
    0x1EE2D: ["<font>", 0x0646],
    0x1EE2E: ["<font>", 0x0633],
    0x1EE2F: ["<font>", 0x0639],
    0x1EE30: ["<font>", 0x0641],
    0x1EE31: ["<font>", 0x0635],
    0x1EE32: ["<font>", 0x0642],
    0x1EE34: ["<font>", 0x0634],
    0x1EE35: ["<font>", 0x062A],
    0x1EE36: ["<font>", 0x062B],
    0x1EE37: ["<font>", 0x062E],
    0x1EE39: ["<font>", 0x0636],
    0x1EE3B: ["<font>", 0x063A],
    0x1EE42: ["<font>", 0x062C],
    0x1EE47: ["<font>", 0x062D],
    0x1EE49: ["<font>", 0x064A],
    0x1EE4B: ["<font>", 0x0644],
    0x1EE4D: ["<font>", 0x0646],
    0x1EE4E: ["<font>", 0x0633],
    0x1EE4F: ["<font>", 0x0639],
    0x1EE51: ["<font>", 0x0635],
    0x1EE52: ["<font>", 0x0642],
    0x1EE54: ["<font>", 0x0634],
    0x1EE57: ["<font>", 0x062E],
    0x1EE59: ["<font>", 0x0636],
    0x1EE5B: ["<font>", 0x063A],
    0x1EE5D: ["<font>", 0x06BA],
    0x1EE5F: ["<font>", 0x066F],
    0x1EE61: ["<font>", 0x0628],
    0x1EE62: ["<font>", 0x062C],
    0x1EE64: ["<font>", 0x0647],
    0x1EE67: ["<font>", 0x062D],
    0x1EE68: ["<font>", 0x0637],
    0x1EE69: ["<font>", 0x064A],
    0x1EE6A: ["<font>", 0x0643],
    0x1EE6C: ["<font>", 0x0645],
    0x1EE6D: ["<font>", 0x0646],
    0x1EE6E: ["<font>", 0x0633],
    0x1EE6F: ["<font>", 0x0639],
    0x1EE70: ["<font>", 0x0641],
    0x1EE71: ["<font>", 0x0635],
    0x1EE72: ["<font>", 0x0642],
    0x1EE74: ["<font>", 0x0634],
    0x1EE75: ["<font>", 0x062A],
    0x1EE76: ["<font>", 0x062B],
    0x1EE77: ["<font>", 0x062E],
    0x1EE79: ["<font>", 0x0636],
    0x1EE7A: ["<font>", 0x0638],
    0x1EE7B: ["<font>", 0x063A],
    0x1EE7C: ["<font>", 0x066E],
    0x1EE7E: ["<font>", 0x06A1],
    0x1EE80: ["<font>", 0x0627],
    0x1EE81: ["<font>", 0x0628],
    0x1EE82: ["<font>", 0x062C],
    0x1EE83: ["<font>", 0x062F],
    0x1EE84: ["<font>", 0x0647],
    0x1EE85: ["<font>", 0x0648],
    0x1EE86: ["<font>", 0x0632],
    0x1EE87: ["<font>", 0x062D],
    0x1EE88: ["<font>", 0x0637],
    0x1EE89: ["<font>", 0x064A],
    0x1EE8B: ["<font>", 0x0644],
    0x1EE8C: ["<font>", 0x0645],
    0x1EE8D: ["<font>", 0x0646],
    0x1EE8E: ["<font>", 0x0633],
    0x1EE8F: ["<font>", 0x0639],
    0x1EE90: ["<font>", 0x0641],
    0x1EE91: ["<font>", 0x0635],
    0x1EE92: ["<font>", 0x0642],
    0x1EE93: ["<font>", 0x0631],
    0x1EE94: ["<font>", 0x0634],
    0x1EE95: ["<font>", 0x062A],
    0x1EE96: ["<font>", 0x062B],
    0x1EE97: ["<font>", 0x062E],
    0x1EE98: ["<font>", 0x0630],
    0x1EE99: ["<font>", 0x0636],
    0x1EE9A: ["<font>", 0x0638],
    0x1EE9B: ["<font>", 0x063A],
    0x1EEA1: ["<font>", 0x0628],
    0x1EEA2: ["<font>", 0x062C],
    0x1EEA3: ["<font>", 0x062F],
    0x1EEA5: ["<font>", 0x0648],
    0x1EEA6: ["<font>", 0x0632],
    0x1EEA7: ["<font>", 0x062D],
    0x1EEA8: ["<font>", 0x0637],
    0x1EEA9: ["<font>", 0x064A],
    0x1EEAB: ["<font>", 0x0644],
    0x1EEAC: ["<font>", 0x0645],
    0x1EEAD: ["<font>", 0x0646],
    0x1EEAE: ["<font>", 0x0633],
    0x1EEAF: ["<font>", 0x0639],
    0x1EEB0: ["<font>", 0x0641],
    0x1EEB1: ["<font>", 0x0635],
    0x1EEB2: ["<font>", 0x0642],
    0x1EEB3: ["<font>", 0x0631],
    0x1EEB4: ["<font>", 0x0634],
    0x1EEB5: ["<font>", 0x062A],
    0x1EEB6: ["<font>", 0x062B],
    0x1EEB7: ["<font>", 0x062E],
    0x1EEB8: ["<font>", 0x0630],
    0x1EEB9: ["<font>", 0x0636],
    0x1EEBA: ["<font>", 0x0638],
    0x1EEBB: ["<font>", 0x063A],
    0x1F100: ["<compat>", 0x0030, 0x002E],
    0x1F101: ["<compat>", 0x0030, 0x002C],
    0x1F102: ["<compat>", 0x0031, 0x002C],
    0x1F103: ["<compat>", 0x0032, 0x002C],
    0x1F104: ["<compat>", 0x0033, 0x002C],
    0x1F105: ["<compat>", 0x0034, 0x002C],
    0x1F106: ["<compat>", 0x0035, 0x002C],
    0x1F107: ["<compat>", 0x0036, 0x002C],
    0x1F108: ["<compat>", 0x0037, 0x002C],
    0x1F109: ["<compat>", 0x0038, 0x002C],
    0x1F10A: ["<compat>", 0x0039, 0x002C],
    0x1F110: ["<compat>", 0x0028, 0x0041, 0x0029],
    0x1F111: ["<compat>", 0x0028, 0x0042, 0x0029],
    0x1F112: ["<compat>", 0x0028, 0x0043, 0x0029],
    0x1F113: ["<compat>", 0x0028, 0x0044, 0x0029],
    0x1F114: ["<compat>", 0x0028, 0x0045, 0x0029],
    0x1F115: ["<compat>", 0x0028, 0x0046, 0x0029],
    0x1F116: ["<compat>", 0x0028, 0x0047, 0x0029],
    0x1F117: ["<compat>", 0x0028, 0x0048, 0x0029],
    0x1F118: ["<compat>", 0x0028, 0x0049, 0x0029],
    0x1F119: ["<compat>", 0x0028, 0x004A, 0x0029],
    0x1F11A: ["<compat>", 0x0028, 0x004B, 0x0029],
    0x1F11B: ["<compat>", 0x0028, 0x004C, 0x0029],
    0x1F11C: ["<compat>", 0x0028, 0x004D, 0x0029],
    0x1F11D: ["<compat>", 0x0028, 0x004E, 0x0029],
    0x1F11E: ["<compat>", 0x0028, 0x004F, 0x0029],
    0x1F11F: ["<compat>", 0x0028, 0x0050, 0x0029],
    0x1F120: ["<compat>", 0x0028, 0x0051, 0x0029],
    0x1F121: ["<compat>", 0x0028, 0x0052, 0x0029],
    0x1F122: ["<compat>", 0x0028, 0x0053, 0x0029],
    0x1F123: ["<compat>", 0x0028, 0x0054, 0x0029],
    0x1F124: ["<compat>", 0x0028, 0x0055, 0x0029],
    0x1F125: ["<compat>", 0x0028, 0x0056, 0x0029],
    0x1F126: ["<compat>", 0x0028, 0x0057, 0x0029],
    0x1F127: ["<compat>", 0x0028, 0x0058, 0x0029],
    0x1F128: ["<compat>", 0x0028, 0x0059, 0x0029],
    0x1F129: ["<compat>", 0x0028, 0x005A, 0x0029],
    0x1F12A: ["<compat>", 0x3014, 0x0053, 0x3015],
    0x1F12B: ["<circle>", 0x0043],
    0x1F12C: ["<circle>", 0x0052],
    0x1F12D: ["<circle>", 0x0043, 0x0044],
    0x1F12E: ["<circle>", 0x0057, 0x005A],
    0x1F130: ["<square>", 0x0041],
    0x1F131: ["<square>", 0x0042],
    0x1F132: ["<square>", 0x0043],
    0x1F133: ["<square>", 0x0044],
    0x1F134: ["<square>", 0x0045],
    0x1F135: ["<square>", 0x0046],
    0x1F136: ["<square>", 0x0047],
    0x1F137: ["<square>", 0x0048],
    0x1F138: ["<square>", 0x0049],
    0x1F139: ["<square>", 0x004A],
    0x1F13A: ["<square>", 0x004B],
    0x1F13B: ["<square>", 0x004C],
    0x1F13C: ["<square>", 0x004D],
    0x1F13D: ["<square>", 0x004E],
    0x1F13E: ["<square>", 0x004F],
    0x1F13F: ["<square>", 0x0050],
    0x1F140: ["<square>", 0x0051],
    0x1F141: ["<square>", 0x0052],
    0x1F142: ["<square>", 0x0053],
    0x1F143: ["<square>", 0x0054],
    0x1F144: ["<square>", 0x0055],
    0x1F145: ["<square>", 0x0056],
    0x1F146: ["<square>", 0x0057],
    0x1F147: ["<square>", 0x0058],
    0x1F148: ["<square>", 0x0059],
    0x1F149: ["<square>", 0x005A],
    0x1F14A: ["<square>", 0x0048, 0x0056],
    0x1F14B: ["<square>", 0x004D, 0x0056],
    0x1F14C: ["<square>", 0x0053, 0x0044],
    0x1F14D: ["<square>", 0x0053, 0x0053],
    0x1F14E: ["<square>", 0x0050, 0x0050, 0x0056],
    0x1F14F: ["<square>", 0x0057, 0x0043],
    0x1F16A: ["<super>", 0x004D, 0x0043],
    0x1F16B: ["<super>", 0x004D, 0x0044],
    0x1F16C: ["<super>", 0x004D, 0x0052],
    0x1F190: ["<square>", 0x0044, 0x004A],
    0x1F200: ["<square>", 0x307B, 0x304B],
    0x1F201: ["<square>", 0x30B3, 0x30B3],
    0x1F202: ["<square>", 0x30B5],
    0x1F210: ["<square>", 0x624B],
    0x1F211: ["<square>", 0x5B57],
    0x1F212: ["<square>", 0x53CC],
    0x1F213: ["<square>", 0x30C7],
    0x1F214: ["<square>", 0x4E8C],
    0x1F215: ["<square>", 0x591A],
    0x1F216: ["<square>", 0x89E3],
    0x1F217: ["<square>", 0x5929],
    0x1F218: ["<square>", 0x4EA4],
    0x1F219: ["<square>", 0x6620],
    0x1F21A: ["<square>", 0x7121],
    0x1F21B: ["<square>", 0x6599],
    0x1F21C: ["<square>", 0x524D],
    0x1F21D: ["<square>", 0x5F8C],
    0x1F21E: ["<square>", 0x518D],
    0x1F21F: ["<square>", 0x65B0],
    0x1F220: ["<square>", 0x521D],
    0x1F221: ["<square>", 0x7D42],
    0x1F222: ["<square>", 0x751F],
    0x1F223: ["<square>", 0x8CA9],
    0x1F224: ["<square>", 0x58F0],
    0x1F225: ["<square>", 0x5439],
    0x1F226: ["<square>", 0x6F14],
    0x1F227: ["<square>", 0x6295],
    0x1F228: ["<square>", 0x6355],
    0x1F229: ["<square>", 0x4E00],
    0x1F22A: ["<square>", 0x4E09],
    0x1F22B: ["<square>", 0x904A],
    0x1F22C: ["<square>", 0x5DE6],
    0x1F22D: ["<square>", 0x4E2D],
    0x1F22E: ["<square>", 0x53F3],
    0x1F22F: ["<square>", 0x6307],
    0x1F230: ["<square>", 0x8D70],
    0x1F231: ["<square>", 0x6253],
    0x1F232: ["<square>", 0x7981],
    0x1F233: ["<square>", 0x7A7A],
    0x1F234: ["<square>", 0x5408],
    0x1F235: ["<square>", 0x6E80],
    0x1F236: ["<square>", 0x6709],
    0x1F237: ["<square>", 0x6708],
    0x1F238: ["<square>", 0x7533],
    0x1F239: ["<square>", 0x5272],
    0x1F23A: ["<square>", 0x55B6],
    0x1F23B: ["<square>", 0x914D],
    0x1F240: ["<compat>", 0x3014, 0x672C, 0x3015],
    0x1F241: ["<compat>", 0x3014, 0x4E09, 0x3015],
    0x1F242: ["<compat>", 0x3014, 0x4E8C, 0x3015],
    0x1F243: ["<compat>", 0x3014, 0x5B89, 0x3015],
    0x1F244: ["<compat>", 0x3014, 0x70B9, 0x3015],
    0x1F245: ["<compat>", 0x3014, 0x6253, 0x3015],
    0x1F246: ["<compat>", 0x3014, 0x76D7, 0x3015],
    0x1F247: ["<compat>", 0x3014, 0x52DD, 0x3015],
    0x1F248: ["<compat>", 0x3014, 0x6557, 0x3015],
    0x1F250: ["<circle>", 0x5F97],
    0x1F251: ["<circle>", 0x53EF],
    0x1FBF0: ["<font>", 0x0030],
    0x1FBF1: ["<font>", 0x0031],
    0x1FBF2: ["<font>", 0x0032],
    0x1FBF3: ["<font>", 0x0033],
    0x1FBF4: ["<font>", 0x0034],
    0x1FBF5: ["<font>", 0x0035],
    0x1FBF6: ["<font>", 0x0036],
    0x1FBF7: ["<font>", 0x0037],
    0x1FBF8: ["<font>", 0x0038],
    0x1FBF9: ["<font>", 0x0039],
    0x2F800: [0x4E3D],
    0x2F801: [0x4E38],
    0x2F802: [0x4E41],
    0x2F803: [0x20122],
    0x2F804: [0x4F60],
    0x2F805: [0x4FAE],
    0x2F806: [0x4FBB],
    0x2F807: [0x5002],
    0x2F808: [0x507A],
    0x2F809: [0x5099],
    0x2F80A: [0x50E7],
    0x2F80B: [0x50CF],
    0x2F80C: [0x349E],
    0x2F80D: [0x2063A],
    0x2F80E: [0x514D],
    0x2F80F: [0x5154],
    0x2F810: [0x5164],
    0x2F811: [0x5177],
    0x2F812: [0x2051C],
    0x2F813: [0x34B9],
    0x2F814: [0x5167],
    0x2F815: [0x518D],
    0x2F816: [0x2054B],
    0x2F817: [0x5197],
    0x2F818: [0x51A4],
    0x2F819: [0x4ECC],
    0x2F81A: [0x51AC],
    0x2F81B: [0x51B5],
    0x2F81C: [0x291DF],
    0x2F81D: [0x51F5],
    0x2F81E: [0x5203],
    0x2F81F: [0x34DF],
    0x2F820: [0x523B],
    0x2F821: [0x5246],
    0x2F822: [0x5272],
    0x2F823: [0x5277],
    0x2F824: [0x3515],
    0x2F825: [0x52C7],
    0x2F826: [0x52C9],
    0x2F827: [0x52E4],
    0x2F828: [0x52FA],
    0x2F829: [0x5305],
    0x2F82A: [0x5306],
    0x2F82B: [0x5317],
    0x2F82C: [0x5349],
    0x2F82D: [0x5351],
    0x2F82E: [0x535A],
    0x2F82F: [0x5373],
    0x2F830: [0x537D],
    0x2F831: [0x537F],
    0x2F832: [0x537F],
    0x2F833: [0x537F],
    0x2F834: [0x20A2C],
    0x2F835: [0x7070],
    0x2F836: [0x53CA],
    0x2F837: [0x53DF],
    0x2F838: [0x20B63],
    0x2F839: [0x53EB],
    0x2F83A: [0x53F1],
    0x2F83B: [0x5406],
    0x2F83C: [0x549E],
    0x2F83D: [0x5438],
    0x2F83E: [0x5448],
    0x2F83F: [0x5468],
    0x2F840: [0x54A2],
    0x2F841: [0x54F6],
    0x2F842: [0x5510],
    0x2F843: [0x5553],
    0x2F844: [0x5563],
    0x2F845: [0x5584],
    0x2F846: [0x5584],
    0x2F847: [0x5599],
    0x2F848: [0x55AB],
    0x2F849: [0x55B3],
    0x2F84A: [0x55C2],
    0x2F84B: [0x5716],
    0x2F84C: [0x5606],
    0x2F84D: [0x5717],
    0x2F84E: [0x5651],
    0x2F84F: [0x5674],
    0x2F850: [0x5207],
    0x2F851: [0x58EE],
    0x2F852: [0x57CE],
    0x2F853: [0x57F4],
    0x2F854: [0x580D],
    0x2F855: [0x578B],
    0x2F856: [0x5832],
    0x2F857: [0x5831],
    0x2F858: [0x58AC],
    0x2F859: [0x214E4],
    0x2F85A: [0x58F2],
    0x2F85B: [0x58F7],
    0x2F85C: [0x5906],
    0x2F85D: [0x591A],
    0x2F85E: [0x5922],
    0x2F85F: [0x5962],
    0x2F860: [0x216A8],
    0x2F861: [0x216EA],
    0x2F862: [0x59EC],
    0x2F863: [0x5A1B],
    0x2F864: [0x5A27],
    0x2F865: [0x59D8],
    0x2F866: [0x5A66],
    0x2F867: [0x36EE],
    0x2F868: [0x36FC],
    0x2F869: [0x5B08],
    0x2F86A: [0x5B3E],
    0x2F86B: [0x5B3E],
    0x2F86C: [0x219C8],
    0x2F86D: [0x5BC3],
    0x2F86E: [0x5BD8],
    0x2F86F: [0x5BE7],
    0x2F870: [0x5BF3],
    0x2F871: [0x21B18],
    0x2F872: [0x5BFF],
    0x2F873: [0x5C06],
    0x2F874: [0x5F53],
    0x2F875: [0x5C22],
    0x2F876: [0x3781],
    0x2F877: [0x5C60],
    0x2F878: [0x5C6E],
    0x2F879: [0x5CC0],
    0x2F87A: [0x5C8D],
    0x2F87B: [0x21DE4],
    0x2F87C: [0x5D43],
    0x2F87D: [0x21DE6],
    0x2F87E: [0x5D6E],
    0x2F87F: [0x5D6B],
    0x2F880: [0x5D7C],
    0x2F881: [0x5DE1],
    0x2F882: [0x5DE2],
    0x2F883: [0x382F],
    0x2F884: [0x5DFD],
    0x2F885: [0x5E28],
    0x2F886: [0x5E3D],
    0x2F887: [0x5E69],
    0x2F888: [0x3862],
    0x2F889: [0x22183],
    0x2F88A: [0x387C],
    0x2F88B: [0x5EB0],
    0x2F88C: [0x5EB3],
    0x2F88D: [0x5EB6],
    0x2F88E: [0x5ECA],
    0x2F88F: [0x2A392],
    0x2F890: [0x5EFE],
    0x2F891: [0x22331],
    0x2F892: [0x22331],
    0x2F893: [0x8201],
    0x2F894: [0x5F22],
    0x2F895: [0x5F22],
    0x2F896: [0x38C7],
    0x2F897: [0x232B8],
    0x2F898: [0x261DA],
    0x2F899: [0x5F62],
    0x2F89A: [0x5F6B],
    0x2F89B: [0x38E3],
    0x2F89C: [0x5F9A],
    0x2F89D: [0x5FCD],
    0x2F89E: [0x5FD7],
    0x2F89F: [0x5FF9],
    0x2F8A0: [0x6081],
    0x2F8A1: [0x393A],
    0x2F8A2: [0x391C],
    0x2F8A3: [0x6094],
    0x2F8A4: [0x226D4],
    0x2F8A5: [0x60C7],
    0x2F8A6: [0x6148],
    0x2F8A7: [0x614C],
    0x2F8A8: [0x614E],
    0x2F8A9: [0x614C],
    0x2F8AA: [0x617A],
    0x2F8AB: [0x618E],
    0x2F8AC: [0x61B2],
    0x2F8AD: [0x61A4],
    0x2F8AE: [0x61AF],
    0x2F8AF: [0x61DE],
    0x2F8B0: [0x61F2],
    0x2F8B1: [0x61F6],
    0x2F8B2: [0x6210],
    0x2F8B3: [0x621B],
    0x2F8B4: [0x625D],
    0x2F8B5: [0x62B1],
    0x2F8B6: [0x62D4],
    0x2F8B7: [0x6350],
    0x2F8B8: [0x22B0C],
    0x2F8B9: [0x633D],
    0x2F8BA: [0x62FC],
    0x2F8BB: [0x6368],
    0x2F8BC: [0x6383],
    0x2F8BD: [0x63E4],
    0x2F8BE: [0x22BF1],
    0x2F8BF: [0x6422],
    0x2F8C0: [0x63C5],
    0x2F8C1: [0x63A9],
    0x2F8C2: [0x3A2E],
    0x2F8C3: [0x6469],
    0x2F8C4: [0x647E],
    0x2F8C5: [0x649D],
    0x2F8C6: [0x6477],
    0x2F8C7: [0x3A6C],
    0x2F8C8: [0x654F],
    0x2F8C9: [0x656C],
    0x2F8CA: [0x2300A],
    0x2F8CB: [0x65E3],
    0x2F8CC: [0x66F8],
    0x2F8CD: [0x6649],
    0x2F8CE: [0x3B19],
    0x2F8CF: [0x6691],
    0x2F8D0: [0x3B08],
    0x2F8D1: [0x3AE4],
    0x2F8D2: [0x5192],
    0x2F8D3: [0x5195],
    0x2F8D4: [0x6700],
    0x2F8D5: [0x669C],
    0x2F8D6: [0x80AD],
    0x2F8D7: [0x43D9],
    0x2F8D8: [0x6717],
    0x2F8D9: [0x671B],
    0x2F8DA: [0x6721],
    0x2F8DB: [0x675E],
    0x2F8DC: [0x6753],
    0x2F8DD: [0x233C3],
    0x2F8DE: [0x3B49],
    0x2F8DF: [0x67FA],
    0x2F8E0: [0x6785],
    0x2F8E1: [0x6852],
    0x2F8E2: [0x6885],
    0x2F8E3: [0x2346D],
    0x2F8E4: [0x688E],
    0x2F8E5: [0x681F],
    0x2F8E6: [0x6914],
    0x2F8E7: [0x3B9D],
    0x2F8E8: [0x6942],
    0x2F8E9: [0x69A3],
    0x2F8EA: [0x69EA],
    0x2F8EB: [0x6AA8],
    0x2F8EC: [0x236A3],
    0x2F8ED: [0x6ADB],
    0x2F8EE: [0x3C18],
    0x2F8EF: [0x6B21],
    0x2F8F0: [0x238A7],
    0x2F8F1: [0x6B54],
    0x2F8F2: [0x3C4E],
    0x2F8F3: [0x6B72],
    0x2F8F4: [0x6B9F],
    0x2F8F5: [0x6BBA],
    0x2F8F6: [0x6BBB],
    0x2F8F7: [0x23A8D],
    0x2F8F8: [0x21D0B],
    0x2F8F9: [0x23AFA],
    0x2F8FA: [0x6C4E],
    0x2F8FB: [0x23CBC],
    0x2F8FC: [0x6CBF],
    0x2F8FD: [0x6CCD],
    0x2F8FE: [0x6C67],
    0x2F8FF: [0x6D16],
    0x2F900: [0x6D3E],
    0x2F901: [0x6D77],
    0x2F902: [0x6D41],
    0x2F903: [0x6D69],
    0x2F904: [0x6D78],
    0x2F905: [0x6D85],
    0x2F906: [0x23D1E],
    0x2F907: [0x6D34],
    0x2F908: [0x6E2F],
    0x2F909: [0x6E6E],
    0x2F90A: [0x3D33],
    0x2F90B: [0x6ECB],
    0x2F90C: [0x6EC7],
    0x2F90D: [0x23ED1],
    0x2F90E: [0x6DF9],
    0x2F90F: [0x6F6E],
    0x2F910: [0x23F5E],
    0x2F911: [0x23F8E],
    0x2F912: [0x6FC6],
    0x2F913: [0x7039],
    0x2F914: [0x701E],
    0x2F915: [0x701B],
    0x2F916: [0x3D96],
    0x2F917: [0x704A],
    0x2F918: [0x707D],
    0x2F919: [0x7077],
    0x2F91A: [0x70AD],
    0x2F91B: [0x20525],
    0x2F91C: [0x7145],
    0x2F91D: [0x24263],
    0x2F91E: [0x719C],
    0x2F91F: [0x243AB],
    0x2F920: [0x7228],
    0x2F921: [0x7235],
    0x2F922: [0x7250],
    0x2F923: [0x24608],
    0x2F924: [0x7280],
    0x2F925: [0x7295],
    0x2F926: [0x24735],
    0x2F927: [0x24814],
    0x2F928: [0x737A],
    0x2F929: [0x738B],
    0x2F92A: [0x3EAC],
    0x2F92B: [0x73A5],
    0x2F92C: [0x3EB8],
    0x2F92D: [0x3EB8],
    0x2F92E: [0x7447],
    0x2F92F: [0x745C],
    0x2F930: [0x7471],
    0x2F931: [0x7485],
    0x2F932: [0x74CA],
    0x2F933: [0x3F1B],
    0x2F934: [0x7524],
    0x2F935: [0x24C36],
    0x2F936: [0x753E],
    0x2F937: [0x24C92],
    0x2F938: [0x7570],
    0x2F939: [0x2219F],
    0x2F93A: [0x7610],
    0x2F93B: [0x24FA1],
    0x2F93C: [0x24FB8],
    0x2F93D: [0x25044],
    0x2F93E: [0x3FFC],
    0x2F93F: [0x4008],
    0x2F940: [0x76F4],
    0x2F941: [0x250F3],
    0x2F942: [0x250F2],
    0x2F943: [0x25119],
    0x2F944: [0x25133],
    0x2F945: [0x771E],
    0x2F946: [0x771F],
    0x2F947: [0x771F],
    0x2F948: [0x774A],
    0x2F949: [0x4039],
    0x2F94A: [0x778B],
    0x2F94B: [0x4046],
    0x2F94C: [0x4096],
    0x2F94D: [0x2541D],
    0x2F94E: [0x784E],
    0x2F94F: [0x788C],
    0x2F950: [0x78CC],
    0x2F951: [0x40E3],
    0x2F952: [0x25626],
    0x2F953: [0x7956],
    0x2F954: [0x2569A],
    0x2F955: [0x256C5],
    0x2F956: [0x798F],
    0x2F957: [0x79EB],
    0x2F958: [0x412F],
    0x2F959: [0x7A40],
    0x2F95A: [0x7A4A],
    0x2F95B: [0x7A4F],
    0x2F95C: [0x2597C],
    0x2F95D: [0x25AA7],
    0x2F95E: [0x25AA7],
    0x2F95F: [0x7AEE],
    0x2F960: [0x4202],
    0x2F961: [0x25BAB],
    0x2F962: [0x7BC6],
    0x2F963: [0x7BC9],
    0x2F964: [0x4227],
    0x2F965: [0x25C80],
    0x2F966: [0x7CD2],
    0x2F967: [0x42A0],
    0x2F968: [0x7CE8],
    0x2F969: [0x7CE3],
    0x2F96A: [0x7D00],
    0x2F96B: [0x25F86],
    0x2F96C: [0x7D63],
    0x2F96D: [0x4301],
    0x2F96E: [0x7DC7],
    0x2F96F: [0x7E02],
    0x2F970: [0x7E45],
    0x2F971: [0x4334],
    0x2F972: [0x26228],
    0x2F973: [0x26247],
    0x2F974: [0x4359],
    0x2F975: [0x262D9],
    0x2F976: [0x7F7A],
    0x2F977: [0x2633E],
    0x2F978: [0x7F95],
    0x2F979: [0x7FFA],
    0x2F97A: [0x8005],
    0x2F97B: [0x264DA],
    0x2F97C: [0x26523],
    0x2F97D: [0x8060],
    0x2F97E: [0x265A8],
    0x2F97F: [0x8070],
    0x2F980: [0x2335F],
    0x2F981: [0x43D5],
    0x2F982: [0x80B2],
    0x2F983: [0x8103],
    0x2F984: [0x440B],
    0x2F985: [0x813E],
    0x2F986: [0x5AB5],
    0x2F987: [0x267A7],
    0x2F988: [0x267B5],
    0x2F989: [0x23393],
    0x2F98A: [0x2339C],
    0x2F98B: [0x8201],
    0x2F98C: [0x8204],
    0x2F98D: [0x8F9E],
    0x2F98E: [0x446B],
    0x2F98F: [0x8291],
    0x2F990: [0x828B],
    0x2F991: [0x829D],
    0x2F992: [0x52B3],
    0x2F993: [0x82B1],
    0x2F994: [0x82B3],
    0x2F995: [0x82BD],
    0x2F996: [0x82E6],
    0x2F997: [0x26B3C],
    0x2F998: [0x82E5],
    0x2F999: [0x831D],
    0x2F99A: [0x8363],
    0x2F99B: [0x83AD],
    0x2F99C: [0x8323],
    0x2F99D: [0x83BD],
    0x2F99E: [0x83E7],
    0x2F99F: [0x8457],
    0x2F9A0: [0x8353],
    0x2F9A1: [0x83CA],
    0x2F9A2: [0x83CC],
    0x2F9A3: [0x83DC],
    0x2F9A4: [0x26C36],
    0x2F9A5: [0x26D6B],
    0x2F9A6: [0x26CD5],
    0x2F9A7: [0x452B],
    0x2F9A8: [0x84F1],
    0x2F9A9: [0x84F3],
    0x2F9AA: [0x8516],
    0x2F9AB: [0x273CA],
    0x2F9AC: [0x8564],
    0x2F9AD: [0x26F2C],
    0x2F9AE: [0x455D],
    0x2F9AF: [0x4561],
    0x2F9B0: [0x26FB1],
    0x2F9B1: [0x270D2],
    0x2F9B2: [0x456B],
    0x2F9B3: [0x8650],
    0x2F9B4: [0x865C],
    0x2F9B5: [0x8667],
    0x2F9B6: [0x8669],
    0x2F9B7: [0x86A9],
    0x2F9B8: [0x8688],
    0x2F9B9: [0x870E],
    0x2F9BA: [0x86E2],
    0x2F9BB: [0x8779],
    0x2F9BC: [0x8728],
    0x2F9BD: [0x876B],
    0x2F9BE: [0x8786],
    0x2F9BF: [0x45D7],
    0x2F9C0: [0x87E1],
    0x2F9C1: [0x8801],
    0x2F9C2: [0x45F9],
    0x2F9C3: [0x8860],
    0x2F9C4: [0x8863],
    0x2F9C5: [0x27667],
    0x2F9C6: [0x88D7],
    0x2F9C7: [0x88DE],
    0x2F9C8: [0x4635],
    0x2F9C9: [0x88FA],
    0x2F9CA: [0x34BB],
    0x2F9CB: [0x278AE],
    0x2F9CC: [0x27966],
    0x2F9CD: [0x46BE],
    0x2F9CE: [0x46C7],
    0x2F9CF: [0x8AA0],
    0x2F9D0: [0x8AED],
    0x2F9D1: [0x8B8A],
    0x2F9D2: [0x8C55],
    0x2F9D3: [0x27CA8],
    0x2F9D4: [0x8CAB],
    0x2F9D5: [0x8CC1],
    0x2F9D6: [0x8D1B],
    0x2F9D7: [0x8D77],
    0x2F9D8: [0x27F2F],
    0x2F9D9: [0x20804],
    0x2F9DA: [0x8DCB],
    0x2F9DB: [0x8DBC],
    0x2F9DC: [0x8DF0],
    0x2F9DD: [0x208DE],
    0x2F9DE: [0x8ED4],
    0x2F9DF: [0x8F38],
    0x2F9E0: [0x285D2],
    0x2F9E1: [0x285ED],
    0x2F9E2: [0x9094],
    0x2F9E3: [0x90F1],
    0x2F9E4: [0x9111],
    0x2F9E5: [0x2872E],
    0x2F9E6: [0x911B],
    0x2F9E7: [0x9238],
    0x2F9E8: [0x92D7],
    0x2F9E9: [0x92D8],
    0x2F9EA: [0x927C],
    0x2F9EB: [0x93F9],
    0x2F9EC: [0x9415],
    0x2F9ED: [0x28BFA],
    0x2F9EE: [0x958B],
    0x2F9EF: [0x4995],
    0x2F9F0: [0x95B7],
    0x2F9F1: [0x28D77],
    0x2F9F2: [0x49E6],
    0x2F9F3: [0x96C3],
    0x2F9F4: [0x5DB2],
    0x2F9F5: [0x9723],
    0x2F9F6: [0x29145],
    0x2F9F7: [0x2921A],
    0x2F9F8: [0x4A6E],
    0x2F9F9: [0x4A76],
    0x2F9FA: [0x97E0],
    0x2F9FB: [0x2940A],
    0x2F9FC: [0x4AB2],
    0x2F9FD: [0x29496],
    0x2F9FE: [0x980B],
    0x2F9FF: [0x980B],
    0x2FA00: [0x9829],
    0x2FA01: [0x295B6],
    0x2FA02: [0x98E2],
    0x2FA03: [0x4B33],
    0x2FA04: [0x9929],
    0x2FA05: [0x99A7],
    0x2FA06: [0x99C2],
    0x2FA07: [0x99FE],
    0x2FA08: [0x4BCE],
    0x2FA09: [0x29B30],
    0x2FA0A: [0x9B12],
    0x2FA0B: [0x9C40],
    0x2FA0C: [0x9CFD],
    0x2FA0D: [0x4CCE],
    0x2FA0E: [0x4CED],
    0x2FA0F: [0x9D67],
    0x2FA10: [0x2A0CE],
    0x2FA11: [0x4CF8],
    0x2FA12: [0x2A105],
    0x2FA13: [0x2A20E],
    0x2FA14: [0x2A291],
    0x2FA15: [0x9EBB],
    0x2FA16: [0x4D56],
    0x2FA17: [0x9EF9],
    0x2FA18: [0x9EFE],
    0x2FA19: [0x9F05],
    0x2FA1A: [0x9F0F],
    0x2FA1B: [0x9F16],
    0x2FA1C: [0x9F3B],
    0x2FA1D: [0x2A600],
}

# Dictionary mapping characters with non-zero canonical combining class values
# to their corresponding values
_NON_ZERO_CCC_TABLE = {
    0x00300: 230,
    0x00301: 230,
    0x00302: 230,
    0x00303: 230,
    0x00304: 230,
    0x00305: 230,
    0x00306: 230,
    0x00307: 230,
    0x00308: 230,
    0x00309: 230,
    0x0030A: 230,
    0x0030B: 230,
    0x0030C: 230,
    0x0030D: 230,
    0x0030E: 230,
    0x0030F: 230,
    0x00310: 230,
    0x00311: 230,
    0x00312: 230,
    0x00313: 230,
    0x00314: 230,
    0x00315: 232,
    0x00316: 220,
    0x00317: 220,
    0x00318: 220,
    0x00319: 220,
    0x0031A: 232,
    0x0031B: 216,
    0x0031C: 220,
    0x0031D: 220,
    0x0031E: 220,
    0x0031F: 220,
    0x00320: 220,
    0x00321: 202,
    0x00322: 202,
    0x00323: 220,
    0x00324: 220,
    0x00325: 220,
    0x00326: 220,
    0x00327: 202,
    0x00328: 202,
    0x00329: 220,
    0x0032A: 220,
    0x0032B: 220,
    0x0032C: 220,
    0x0032D: 220,
    0x0032E: 220,
    0x0032F: 220,
    0x00330: 220,
    0x00331: 220,
    0x00332: 220,
    0x00333: 220,
    0x00334:   1,
    0x00335:   1,
    0x00336:   1,
    0x00337:   1,
    0x00338:   1,
    0x00339: 220,
    0x0033A: 220,
    0x0033B: 220,
    0x0033C: 220,
    0x0033D: 230,
    0x0033E: 230,
    0x0033F: 230,
    0x00340: 230,
    0x00341: 230,
    0x00342: 230,
    0x00343: 230,
    0x00344: 230,
    0x00345: 240,
    0x00346: 230,
    0x00347: 220,
    0x00348: 220,
    0x00349: 220,
    0x0034A: 230,
    0x0034B: 230,
    0x0034C: 230,
    0x0034D: 220,
    0x0034E: 220,
    0x00350: 230,
    0x00351: 230,
    0x00352: 230,
    0x00353: 220,
    0x00354: 220,
    0x00355: 220,
    0x00356: 220,
    0x00357: 230,
    0x00358: 232,
    0x00359: 220,
    0x0035A: 220,
    0x0035B: 230,
    0x0035C: 233,
    0x0035D: 234,
    0x0035E: 234,
    0x0035F: 233,
    0x00360: 234,
    0x00361: 234,
    0x00362: 233,
    0x00363: 230,
    0x00364: 230,
    0x00365: 230,
    0x00366: 230,
    0x00367: 230,
    0x00368: 230,
    0x00369: 230,
    0x0036A: 230,
    0x0036B: 230,
    0x0036C: 230,
    0x0036D: 230,
    0x0036E: 230,
    0x0036F: 230,
    0x00483: 230,
    0x00484: 230,
    0x00485: 230,
    0x00486: 230,
    0x00487: 230,
    0x00591: 220,
    0x00592: 230,
    0x00593: 230,
    0x00594: 230,
    0x00595: 230,
    0x00596: 220,
    0x00597: 230,
    0x00598: 230,
    0x00599: 230,
    0x0059A: 222,
    0x0059B: 220,
    0x0059C: 230,
    0x0059D: 230,
    0x0059E: 230,
    0x0059F: 230,
    0x005A0: 230,
    0x005A1: 230,
    0x005A2: 220,
    0x005A3: 220,
    0x005A4: 220,
    0x005A5: 220,
    0x005A6: 220,
    0x005A7: 220,
    0x005A8: 230,
    0x005A9: 230,
    0x005AA: 220,
    0x005AB: 230,
    0x005AC: 230,
    0x005AD: 222,
    0x005AE: 228,
    0x005AF: 230,
    0x005B0:  10,
    0x005B1:  11,
    0x005B2:  12,
    0x005B3:  13,
    0x005B4:  14,
    0x005B5:  15,
    0x005B6:  16,
    0x005B7:  17,
    0x005B8:  18,
    0x005B9:  19,
    0x005BA:  19,
    0x005BB:  20,
    0x005BC:  21,
    0x005BD:  22,
    0x005BF:  23,
    0x005C1:  24,
    0x005C2:  25,
    0x005C4: 230,
    0x005C5: 220,
    0x005C7:  18,
    0x00610: 230,
    0x00611: 230,
    0x00612: 230,
    0x00613: 230,
    0x00614: 230,
    0x00615: 230,
    0x00616: 230,
    0x00617: 230,
    0x00618:  30,
    0x00619:  31,
    0x0061A:  32,
    0x0064B:  27,
    0x0064C:  28,
    0x0064D:  29,
    0x0064E:  30,
    0x0064F:  31,
    0x00650:  32,
    0x00651:  33,
    0x00652:  34,
    0x00653: 230,
    0x00654: 230,
    0x00655: 220,
    0x00656: 220,
    0x00657: 230,
    0x00658: 230,
    0x00659: 230,
    0x0065A: 230,
    0x0065B: 230,
    0x0065C: 220,
    0x0065D: 230,
    0x0065E: 230,
    0x0065F: 220,
    0x00670:  35,
    0x006D6: 230,
    0x006D7: 230,
    0x006D8: 230,
    0x006D9: 230,
    0x006DA: 230,
    0x006DB: 230,
    0x006DC: 230,
    0x006DF: 230,
    0x006E0: 230,
    0x006E1: 230,
    0x006E2: 230,
    0x006E3: 220,
    0x006E4: 230,
    0x006E7: 230,
    0x006E8: 230,
    0x006EA: 220,
    0x006EB: 230,
    0x006EC: 230,
    0x006ED: 220,
    0x00711:  36,
    0x00730: 230,
    0x00731: 220,
    0x00732: 230,
    0x00733: 230,
    0x00734: 220,
    0x00735: 230,
    0x00736: 230,
    0x00737: 220,
    0x00738: 220,
    0x00739: 220,
    0x0073A: 230,
    0x0073B: 220,
    0x0073C: 220,
    0x0073D: 230,
    0x0073E: 220,
    0x0073F: 230,
    0x00740: 230,
    0x00741: 230,
    0x00742: 220,
    0x00743: 230,
    0x00744: 220,
    0x00745: 230,
    0x00746: 220,
    0x00747: 230,
    0x00748: 220,
    0x00749: 230,
    0x0074A: 230,
    0x007EB: 230,
    0x007EC: 230,
    0x007ED: 230,
    0x007EE: 230,
    0x007EF: 230,
    0x007F0: 230,
    0x007F1: 230,
    0x007F2: 220,
    0x007F3: 230,
    0x007FD: 220,
    0x00816: 230,
    0x00817: 230,
    0x00818: 230,
    0x00819: 230,
    0x0081B: 230,
    0x0081C: 230,
    0x0081D: 230,
    0x0081E: 230,
    0x0081F: 230,
    0x00820: 230,
    0x00821: 230,
    0x00822: 230,
    0x00823: 230,
    0x00825: 230,
    0x00826: 230,
    0x00827: 230,
    0x00829: 230,
    0x0082A: 230,
    0x0082B: 230,
    0x0082C: 230,
    0x0082D: 230,
    0x00859: 220,
    0x0085A: 220,
    0x0085B: 220,
    0x00897: 230,
    0x00898: 230,
    0x00899: 220,
    0x0089A: 220,
    0x0089B: 220,
    0x0089C: 230,
    0x0089D: 230,
    0x0089E: 230,
    0x0089F: 230,
    0x008CA: 230,
    0x008CB: 230,
    0x008CC: 230,
    0x008CD: 230,
    0x008CE: 230,
    0x008CF: 220,
    0x008D0: 220,
    0x008D1: 220,
    0x008D2: 220,
    0x008D3: 220,
    0x008D4: 230,
    0x008D5: 230,
    0x008D6: 230,
    0x008D7: 230,
    0x008D8: 230,
    0x008D9: 230,
    0x008DA: 230,
    0x008DB: 230,
    0x008DC: 230,
    0x008DD: 230,
    0x008DE: 230,
    0x008DF: 230,
    0x008E0: 230,
    0x008E1: 230,
    0x008E3: 220,
    0x008E4: 230,
    0x008E5: 230,
    0x008E6: 220,
    0x008E7: 230,
    0x008E8: 230,
    0x008E9: 220,
    0x008EA: 230,
    0x008EB: 230,
    0x008EC: 230,
    0x008ED: 220,
    0x008EE: 220,
    0x008EF: 220,
    0x008F0:  27,
    0x008F1:  28,
    0x008F2:  29,
    0x008F3: 230,
    0x008F4: 230,
    0x008F5: 230,
    0x008F6: 220,
    0x008F7: 230,
    0x008F8: 230,
    0x008F9: 220,
    0x008FA: 220,
    0x008FB: 230,
    0x008FC: 230,
    0x008FD: 230,
    0x008FE: 230,
    0x008FF: 230,
    0x0093C:   7,
    0x0094D:   9,
    0x00951: 230,
    0x00952: 220,
    0x00953: 230,
    0x00954: 230,
    0x009BC:   7,
    0x009CD:   9,
    0x009FE: 230,
    0x00A3C:   7,
    0x00A4D:   9,
    0x00ABC:   7,
    0x00ACD:   9,
    0x00B3C:   7,
    0x00B4D:   9,
    0x00BCD:   9,
    0x00C3C:   7,
    0x00C4D:   9,
    0x00C55:  84,
    0x00C56:  91,
    0x00CBC:   7,
    0x00CCD:   9,
    0x00D3B:   9,
    0x00D3C:   9,
    0x00D4D:   9,
    0x00DCA:   9,
    0x00E38: 103,
    0x00E39: 103,
    0x00E3A:   9,
    0x00E48: 107,
    0x00E49: 107,
    0x00E4A: 107,
    0x00E4B: 107,
    0x00EB8: 118,
    0x00EB9: 118,
    0x00EBA:   9,
    0x00EC8: 122,
    0x00EC9: 122,
    0x00ECA: 122,
    0x00ECB: 122,
    0x00F18: 220,
    0x00F19: 220,
    0x00F35: 220,
    0x00F37: 220,
    0x00F39: 216,
    0x00F71: 129,
    0x00F72: 130,
    0x00F74: 132,
    0x00F7A: 130,
    0x00F7B: 130,
    0x00F7C: 130,
    0x00F7D: 130,
    0x00F80: 130,
    0x00F82: 230,
    0x00F83: 230,
    0x00F84:   9,
    0x00F86: 230,
    0x00F87: 230,
    0x00FC6: 220,
    0x01037:   7,
    0x01039:   9,
    0x0103A:   9,
    0x0108D: 220,
    0x0135D: 230,
    0x0135E: 230,
    0x0135F: 230,
    0x01714:   9,
    0x01715:   9,
    0x01734:   9,
    0x017D2:   9,
    0x017DD: 230,
    0x018A9: 228,
    0x01939: 222,
    0x0193A: 230,
    0x0193B: 220,
    0x01A17: 230,
    0x01A18: 220,
    0x01A60:   9,
    0x01A75: 230,
    0x01A76: 230,
    0x01A77: 230,
    0x01A78: 230,
    0x01A79: 230,
    0x01A7A: 230,
    0x01A7B: 230,
    0x01A7C: 230,
    0x01A7F: 220,
    0x01AB0: 230,
    0x01AB1: 230,
    0x01AB2: 230,
    0x01AB3: 230,
    0x01AB4: 230,
    0x01AB5: 220,
    0x01AB6: 220,
    0x01AB7: 220,
    0x01AB8: 220,
    0x01AB9: 220,
    0x01ABA: 220,
    0x01ABB: 230,
    0x01ABC: 230,
    0x01ABD: 220,
    0x01ABF: 220,
    0x01AC0: 220,
    0x01AC1: 230,
    0x01AC2: 230,
    0x01AC3: 220,
    0x01AC4: 220,
    0x01AC5: 230,
    0x01AC6: 230,
    0x01AC7: 230,
    0x01AC8: 230,
    0x01AC9: 230,
    0x01ACA: 220,
    0x01ACB: 230,
    0x01ACC: 230,
    0x01ACD: 230,
    0x01ACE: 230,
    0x01B34:   7,
    0x01B44:   9,
    0x01B6B: 230,
    0x01B6C: 220,
    0x01B6D: 230,
    0x01B6E: 230,
    0x01B6F: 230,
    0x01B70: 230,
    0x01B71: 230,
    0x01B72: 230,
    0x01B73: 230,
    0x01BAA:   9,
    0x01BAB:   9,
    0x01BE6:   7,
    0x01BF2:   9,
    0x01BF3:   9,
    0x01C37:   7,
    0x01CD0: 230,
    0x01CD1: 230,
    0x01CD2: 230,
    0x01CD4:   1,
    0x01CD5: 220,
    0x01CD6: 220,
    0x01CD7: 220,
    0x01CD8: 220,
    0x01CD9: 220,
    0x01CDA: 230,
    0x01CDB: 230,
    0x01CDC: 220,
    0x01CDD: 220,
    0x01CDE: 220,
    0x01CDF: 220,
    0x01CE0: 230,
    0x01CE2:   1,
    0x01CE3:   1,
    0x01CE4:   1,
    0x01CE5:   1,
    0x01CE6:   1,
    0x01CE7:   1,
    0x01CE8:   1,
    0x01CED: 220,
    0x01CF4: 230,
    0x01CF8: 230,
    0x01CF9: 230,
    0x01DC0: 230,
    0x01DC1: 230,
    0x01DC2: 220,
    0x01DC3: 230,
    0x01DC4: 230,
    0x01DC5: 230,
    0x01DC6: 230,
    0x01DC7: 230,
    0x01DC8: 230,
    0x01DC9: 230,
    0x01DCA: 220,
    0x01DCB: 230,
    0x01DCC: 230,
    0x01DCD: 234,
    0x01DCE: 214,
    0x01DCF: 220,
    0x01DD0: 202,
    0x01DD1: 230,
    0x01DD2: 230,
    0x01DD3: 230,
    0x01DD4: 230,
    0x01DD5: 230,
    0x01DD6: 230,
    0x01DD7: 230,
    0x01DD8: 230,
    0x01DD9: 230,
    0x01DDA: 230,
    0x01DDB: 230,
    0x01DDC: 230,
    0x01DDD: 230,
    0x01DDE: 230,
    0x01DDF: 230,
    0x01DE0: 230,
    0x01DE1: 230,
    0x01DE2: 230,
    0x01DE3: 230,
    0x01DE4: 230,
    0x01DE5: 230,
    0x01DE6: 230,
    0x01DE7: 230,
    0x01DE8: 230,
    0x01DE9: 230,
    0x01DEA: 230,
    0x01DEB: 230,
    0x01DEC: 230,
    0x01DED: 230,
    0x01DEE: 230,
    0x01DEF: 230,
    0x01DF0: 230,
    0x01DF1: 230,
    0x01DF2: 230,
    0x01DF3: 230,
    0x01DF4: 230,
    0x01DF5: 230,
    0x01DF6: 232,
    0x01DF7: 228,
    0x01DF8: 228,
    0x01DF9: 220,
    0x01DFA: 218,
    0x01DFB: 230,
    0x01DFC: 233,
    0x01DFD: 220,
    0x01DFE: 230,
    0x01DFF: 220,
    0x020D0: 230,
    0x020D1: 230,
    0x020D2:   1,
    0x020D3:   1,
    0x020D4: 230,
    0x020D5: 230,
    0x020D6: 230,
    0x020D7: 230,
    0x020D8:   1,
    0x020D9:   1,
    0x020DA:   1,
    0x020DB: 230,
    0x020DC: 230,
    0x020E1: 230,
    0x020E5:   1,
    0x020E6:   1,
    0x020E7: 230,
    0x020E8: 220,
    0x020E9: 230,
    0x020EA:   1,
    0x020EB:   1,
    0x020EC: 220,
    0x020ED: 220,
    0x020EE: 220,
    0x020EF: 220,
    0x020F0: 230,
    0x02CEF: 230,
    0x02CF0: 230,
    0x02CF1: 230,
    0x02D7F:   9,
    0x02DE0: 230,
    0x02DE1: 230,
    0x02DE2: 230,
    0x02DE3: 230,
    0x02DE4: 230,
    0x02DE5: 230,
    0x02DE6: 230,
    0x02DE7: 230,
    0x02DE8: 230,
    0x02DE9: 230,
    0x02DEA: 230,
    0x02DEB: 230,
    0x02DEC: 230,
    0x02DED: 230,
    0x02DEE: 230,
    0x02DEF: 230,
    0x02DF0: 230,
    0x02DF1: 230,
    0x02DF2: 230,
    0x02DF3: 230,
    0x02DF4: 230,
    0x02DF5: 230,
    0x02DF6: 230,
    0x02DF7: 230,
    0x02DF8: 230,
    0x02DF9: 230,
    0x02DFA: 230,
    0x02DFB: 230,
    0x02DFC: 230,
    0x02DFD: 230,
    0x02DFE: 230,
    0x02DFF: 230,
    0x0302A: 218,
    0x0302B: 228,
    0x0302C: 232,
    0x0302D: 222,
    0x0302E: 224,
    0x0302F: 224,
    0x03099:   8,
    0x0309A:   8,
    0x0A66F: 230,
    0x0A674: 230,
    0x0A675: 230,
    0x0A676: 230,
    0x0A677: 230,
    0x0A678: 230,
    0x0A679: 230,
    0x0A67A: 230,
    0x0A67B: 230,
    0x0A67C: 230,
    0x0A67D: 230,
    0x0A69E: 230,
    0x0A69F: 230,
    0x0A6F0: 230,
    0x0A6F1: 230,
    0x0A806:   9,
    0x0A82C:   9,
    0x0A8C4:   9,
    0x0A8E0: 230,
    0x0A8E1: 230,
    0x0A8E2: 230,
    0x0A8E3: 230,
    0x0A8E4: 230,
    0x0A8E5: 230,
    0x0A8E6: 230,
    0x0A8E7: 230,
    0x0A8E8: 230,
    0x0A8E9: 230,
    0x0A8EA: 230,
    0x0A8EB: 230,
    0x0A8EC: 230,
    0x0A8ED: 230,
    0x0A8EE: 230,
    0x0A8EF: 230,
    0x0A8F0: 230,
    0x0A8F1: 230,
    0x0A92B: 220,
    0x0A92C: 220,
    0x0A92D: 220,
    0x0A953:   9,
    0x0A9B3:   7,
    0x0A9C0:   9,
    0x0AAB0: 230,
    0x0AAB2: 230,
    0x0AAB3: 230,
    0x0AAB4: 220,
    0x0AAB7: 230,
    0x0AAB8: 230,
    0x0AABE: 230,
    0x0AABF: 230,
    0x0AAC1: 230,
    0x0AAF6:   9,
    0x0ABED:   9,
    0x0FB1E:  26,
    0x0FE20: 230,
    0x0FE21: 230,
    0x0FE22: 230,
    0x0FE23: 230,
    0x0FE24: 230,
    0x0FE25: 230,
    0x0FE26: 230,
    0x0FE27: 220,
    0x0FE28: 220,
    0x0FE29: 220,
    0x0FE2A: 220,
    0x0FE2B: 220,
    0x0FE2C: 220,
    0x0FE2D: 220,
    0x0FE2E: 230,
    0x0FE2F: 230,
    0x101FD: 220,
    0x102E0: 220,
    0x10376: 230,
    0x10377: 230,
    0x10378: 230,
    0x10379: 230,
    0x1037A: 230,
    0x10A0D: 220,
    0x10A0F: 230,
    0x10A38: 230,
    0x10A39:   1,
    0x10A3A: 220,
    0x10A3F:   9,
    0x10AE5: 230,
    0x10AE6: 220,
    0x10D24: 230,
    0x10D25: 230,
    0x10D26: 230,
    0x10D27: 230,
    0x10D69: 230,
    0x10D6A: 230,
    0x10D6B: 230,
    0x10D6C: 230,
    0x10D6D: 230,
    0x10EAB: 230,
    0x10EAC: 230,
    0x10EFD: 220,
    0x10EFE: 220,
    0x10EFF: 220,
    0x10F46: 220,
    0x10F47: 220,
    0x10F48: 230,
    0x10F49: 230,
    0x10F4A: 230,
    0x10F4B: 220,
    0x10F4C: 230,
    0x10F4D: 220,
    0x10F4E: 220,
    0x10F4F: 220,
    0x10F50: 220,
    0x10F82: 230,
    0x10F83: 220,
    0x10F84: 230,
    0x10F85: 220,
    0x11046:   9,
    0x11070:   9,
    0x1107F:   9,
    0x110B9:   9,
    0x110BA:   7,
    0x11100: 230,
    0x11101: 230,
    0x11102: 230,
    0x11133:   9,
    0x11134:   9,
    0x11173:   7,
    0x111C0:   9,
    0x111CA:   7,
    0x11235:   9,
    0x11236:   7,
    0x112E9:   7,
    0x112EA:   9,
    0x1133B:   7,
    0x1133C:   7,
    0x1134D:   9,
    0x11366: 230,
    0x11367: 230,
    0x11368: 230,
    0x11369: 230,
    0x1136A: 230,
    0x1136B: 230,
    0x1136C: 230,
    0x11370: 230,
    0x11371: 230,
    0x11372: 230,
    0x11373: 230,
    0x11374: 230,
    0x113CE:   9,
    0x113CF:   9,
    0x113D0:   9,
    0x11442:   9,
    0x11446:   7,
    0x1145E: 230,
    0x114C2:   9,
    0x114C3:   7,
    0x115BF:   9,
    0x115C0:   7,
    0x1163F:   9,
    0x116B6:   9,
    0x116B7:   7,
    0x1172B:   9,
    0x11839:   9,
    0x1183A:   7,
    0x1193D:   9,
    0x1193E:   9,
    0x11943:   7,
    0x119E0:   9,
    0x11A34:   9,
    0x11A47:   9,
    0x11A99:   9,
    0x11C3F:   9,
    0x11D42:   7,
    0x11D44:   9,
    0x11D45:   9,
    0x11D97:   9,
    0x11F41:   9,
    0x11F42:   9,
    0x1612F:   9,
    0x16AF0:   1,
    0x16AF1:   1,
    0x16AF2:   1,
    0x16AF3:   1,
    0x16AF4:   1,
    0x16B30: 230,
    0x16B31: 230,
    0x16B32: 230,
    0x16B33: 230,
    0x16B34: 230,
    0x16B35: 230,
    0x16B36: 230,
    0x16FF0:   6,
    0x16FF1:   6,
    0x1BC9E:   1,
    0x1D165: 216,
    0x1D166: 216,
    0x1D167:   1,
    0x1D168:   1,
    0x1D169:   1,
    0x1D16D: 226,
    0x1D16E: 216,
    0x1D16F: 216,
    0x1D170: 216,
    0x1D171: 216,
    0x1D172: 216,
    0x1D17B: 220,
    0x1D17C: 220,
    0x1D17D: 220,
    0x1D17E: 220,
    0x1D17F: 220,
    0x1D180: 220,
    0x1D181: 220,
    0x1D182: 220,
    0x1D185: 230,
    0x1D186: 230,
    0x1D187: 230,
    0x1D188: 230,
    0x1D189: 230,
    0x1D18A: 220,
    0x1D18B: 220,
    0x1D1AA: 230,
    0x1D1AB: 230,
    0x1D1AC: 230,
    0x1D1AD: 230,
    0x1D242: 230,
    0x1D243: 230,
    0x1D244: 230,
    0x1E000: 230,
    0x1E001: 230,
    0x1E002: 230,
    0x1E003: 230,
    0x1E004: 230,
    0x1E005: 230,
    0x1E006: 230,
    0x1E008: 230,
    0x1E009: 230,
    0x1E00A: 230,
    0x1E00B: 230,
    0x1E00C: 230,
    0x1E00D: 230,
    0x1E00E: 230,
    0x1E00F: 230,
    0x1E010: 230,
    0x1E011: 230,
    0x1E012: 230,
    0x1E013: 230,
    0x1E014: 230,
    0x1E015: 230,
    0x1E016: 230,
    0x1E017: 230,
    0x1E018: 230,
    0x1E01B: 230,
    0x1E01C: 230,
    0x1E01D: 230,
    0x1E01E: 230,
    0x1E01F: 230,
    0x1E020: 230,
    0x1E021: 230,
    0x1E023: 230,
    0x1E024: 230,
    0x1E026: 230,
    0x1E027: 230,
    0x1E028: 230,
    0x1E029: 230,
    0x1E02A: 230,
    0x1E08F: 230,
    0x1E130: 230,
    0x1E131: 230,
    0x1E132: 230,
    0x1E133: 230,
    0x1E134: 230,
    0x1E135: 230,
    0x1E136: 230,
    0x1E2AE: 230,
    0x1E2EC: 230,
    0x1E2ED: 230,
    0x1E2EE: 230,
    0x1E2EF: 230,
    0x1E4EC: 232,
    0x1E4ED: 232,
    0x1E4EE: 220,
    0x1E4EF: 230,
    0x1E5EE: 230,
    0x1E5EF: 220,
    0x1E8D0: 220,
    0x1E8D1: 220,
    0x1E8D2: 220,
    0x1E8D3: 220,
    0x1E8D4: 220,
    0x1E8D5: 220,
    0x1E8D6: 220,
    0x1E944: 230,
    0x1E945: 230,
    0x1E946: 230,
    0x1E947: 230,
    0x1E948: 230,
    0x1E949: 230,
    0x1E94A:   7,
}

# Characters which are excluded from composition
_COMPOSITION_EXCLUSIONS = {
    0x00958,
    0x00959,
    0x0095A,
    0x0095B,
    0x0095C,
    0x0095D,
    0x0095E,
    0x0095F,
    0x009DC,
    0x009DD,
    0x009DF,
    0x00A33,
    0x00A36,
    0x00A59,
    0x00A5A,
    0x00A5B,
    0x00A5E,
    0x00B5C,
    0x00B5D,
    0x00F43,
    0x00F4D,
    0x00F52,
    0x00F57,
    0x00F5C,
    0x00F69,
    0x00F76,
    0x00F78,
    0x00F93,
    0x00F9D,
    0x00FA2,
    0x00FA7,
    0x00FAC,
    0x00FB9,
    0x0FB1D,
    0x0FB1F,
    0x0FB2A,
    0x0FB2B,
    0x0FB2C,
    0x0FB2D,
    0x0FB2E,
    0x0FB2F,
    0x0FB30,
    0x0FB31,
    0x0FB32,
    0x0FB33,
    0x0FB34,
    0x0FB35,
    0x0FB36,
    0x0FB38,
    0x0FB39,
    0x0FB3A,
    0x0FB3B,
    0x0FB3C,
    0x0FB3E,
    0x0FB40,
    0x0FB41,
    0x0FB43,
    0x0FB44,
    0x0FB46,
    0x0FB47,
    0x0FB48,
    0x0FB49,
    0x0FB4A,
    0x0FB4B,
    0x0FB4C,
    0x0FB4D,
    0x0FB4E,
    0x02ADC,
    0x1D15E,
    0x1D15F,
    0x1D160,
    0x1D161,
    0x1D162,
    0x1D163,
    0x1D164,
    0x1D1BB,
    0x1D1BC,
    0x1D1BD,
    0x1D1BE,
    0x1D1BF,
    0x1D1C0,
}

# NFC_Quick_Check=No
# Characters that cannot ever occur in the normalization form C
_NFC__QC_NO = set([
    *range(0x00340, 0x00341 + 1),
    *range(0x00343, 0x00344 + 1),
           0x00374,
           0x0037E,
           0x00387,
    *range(0x00958, 0x0095F + 1),
    *range(0x009DC, 0x009DD + 1),
           0x009DF,
           0x00A33,
           0x00A36,
    *range(0x00A59, 0x00A5B + 1),
           0x00A5E,
    *range(0x00B5C, 0x00B5D + 1),
           0x00F43,
           0x00F4D,
           0x00F52,
           0x00F57,
           0x00F5C,
           0x00F69,
           0x00F73,
    *range(0x00F75, 0x00F76 + 1),
           0x00F78,
           0x00F81,
           0x00F93,
           0x00F9D,
           0x00FA2,
           0x00FA7,
           0x00FAC,
           0x00FB9,
           0x01F71,
           0x01F73,
           0x01F75,
           0x01F77,
           0x01F79,
           0x01F7B,
           0x01F7D,
           0x01FBB,
           0x01FBE,
           0x01FC9,
           0x01FCB,
           0x01FD3,
           0x01FDB,
           0x01FE3,
           0x01FEB,
    *range(0x01FEE, 0x01FEF + 1),
           0x01FF9,
           0x01FFB,
           0x01FFD,
    *range(0x02000, 0x02001 + 1),
           0x02126,
    *range(0x0212A, 0x0212B + 1),
           0x02329,
           0x0232A,
           0x02ADC,
    *range(0x0F900, 0x0FA0D + 1),
           0x0FA10,
           0x0FA12,
    *range(0x0FA15, 0x0FA1E + 1),
           0x0FA20,
           0x0FA22,
    *range(0x0FA25, 0x0FA26 + 1),
    *range(0x0FA2A, 0x0FA6D + 1),
    *range(0x0FA70, 0x0FAD9 + 1),
           0x0FB1D,
           0x0FB1F,
    *range(0x0FB2A, 0x0FB36 + 1),
    *range(0x0FB38, 0x0FB3C + 1),
           0x0FB3E,
    *range(0x0FB40, 0x0FB41 + 1),
    *range(0x0FB43, 0x0FB44 + 1),
    *range(0x0FB46, 0x0FB4E + 1),
    *range(0x1D15E, 0x1D164 + 1),
    *range(0x1D1BB, 0x1D1C0 + 1),
    *range(0x2F800, 0x2FA1D + 1),
])

# NFC_Quick_Check=Maybe
# Characters that may or may not occur in the normalization form C,
# depending on the context
_NFC__QC_MAYBE = set([
    *range(0x00300, 0x00304 + 1),
    *range(0x00306, 0x0030C + 1),
           0x0030F,
           0x00311,
    *range(0x00313, 0x00314 + 1),
           0x0031B,
    *range(0x00323, 0x00328 + 1),
    *range(0x0032D, 0x0032E + 1),
    *range(0x00330, 0x00331 + 1),
           0x00338,
           0x00342,
           0x00345,
    *range(0x00653, 0x00655 + 1),
           0x0093C,
           0x009BE,
           0x009D7,
           0x00B3E,
           0x00B56,
           0x00B57,
           0x00BBE,
           0x00BD7,
           0x00C56,
           0x00CC2,
    *range(0x00CD5, 0x00CD6 + 1),
           0x00D3E,
           0x00D57,
           0x00DCA,
           0x00DCF,
           0x00DDF,
           0x0102E,
    *range(0x01161, 0x01175 + 1),
    *range(0x011A8, 0x011C2 + 1),
           0x01B35,
    *range(0x03099, 0x0309A + 1),
           0x110BA,
           0x11127,
           0x1133E,
           0x11357,
           0x113B8,
           0x113BB,
           0x113C2,
           0x113C5,
    *range(0x113C7, 0x113C9 + 1),
           0x114B0,
           0x114BA,
           0x114BD,
           0x115AF,
           0x11930,
    *range(0x1611E, 0x16129 + 1),
    *range(0x16D67, 0x16D68 + 1),
])

# Code points listed for NFC_Quick_Check=No or NFC_Quick_Check=Maybe
_NFC__QC_NO_OR_MAYBE = _NFC__QC_NO | _NFC__QC_MAYBE

# NFD_Quick_Check=No
# Characters that cannot ever occur in the normalization form D
_NFD__QC_NO = set([
    *range(0x000C0, 0x000C5 + 1),
    *range(0x000C7, 0x000CF + 1),
    *range(0x000D1, 0x000D6 + 1),
    *range(0x000D9, 0x000DD + 1),
    *range(0x000E0, 0x000E5 + 1),
    *range(0x000E7, 0x000EF + 1),
    *range(0x000F1, 0x000F6 + 1),
    *range(0x000F9, 0x000FD + 1),
    *range(0x000FF, 0x0010F + 1),
    *range(0x00112, 0x00125 + 1),
    *range(0x00128, 0x00130 + 1),
    *range(0x00134, 0x00137 + 1),
    *range(0x00139, 0x0013E + 1),
    *range(0x00143, 0x00148 + 1),
    *range(0x0014C, 0x00151 + 1),
    *range(0x00154, 0x00165 + 1),
    *range(0x00168, 0x0017E + 1),
    *range(0x001A0, 0x001A1 + 1),
    *range(0x001AF, 0x001B0 + 1),
    *range(0x001CD, 0x001DC + 1),
    *range(0x001DE, 0x001E3 + 1),
    *range(0x001E6, 0x001F0 + 1),
    *range(0x001F4, 0x001F5 + 1),
    *range(0x001F8, 0x0021B + 1),
    *range(0x0021E, 0x0021F + 1),
    *range(0x00226, 0x00233 + 1),
    *range(0x00340, 0x00341 + 1),
    *range(0x00343, 0x00344 + 1),
           0x00374,
           0x0037E,
           0x00385,
           0x00386,
           0x00387,
    *range(0x00388, 0x0038A + 1),
           0x0038C,
    *range(0x0038E, 0x00390 + 1),
    *range(0x003AA, 0x003B0 + 1),
    *range(0x003CA, 0x003CE + 1),
    *range(0x003D3, 0x003D4 + 1),
    *range(0x00400, 0x00401 + 1),
           0x00403,
           0x00407,
    *range(0x0040C, 0x0040E + 1),
           0x00419,
           0x00439,
    *range(0x00450, 0x00451 + 1),
           0x00453,
           0x00457,
    *range(0x0045C, 0x0045E + 1),
    *range(0x00476, 0x00477 + 1),
    *range(0x004C1, 0x004C2 + 1),
    *range(0x004D0, 0x004D3 + 1),
    *range(0x004D6, 0x004D7 + 1),
    *range(0x004DA, 0x004DF + 1),
    *range(0x004E2, 0x004E7 + 1),
    *range(0x004EA, 0x004F5 + 1),
    *range(0x004F8, 0x004F9 + 1),
    *range(0x00622, 0x00626 + 1),
           0x006C0,
           0x006C2,
           0x006D3,
           0x00929,
           0x00931,
           0x00934,
    *range(0x00958, 0x0095F + 1),
    *range(0x009CB, 0x009CC + 1),
    *range(0x009DC, 0x009DD + 1),
           0x009DF,
           0x00A33,
           0x00A36,
    *range(0x00A59, 0x00A5B + 1),
           0x00A5E,
           0x00B48,
    *range(0x00B4B, 0x00B4C + 1),
    *range(0x00B5C, 0x00B5D + 1),
           0x00B94,
    *range(0x00BCA, 0x00BCC + 1),
           0x00C48,
           0x00CC0,
    *range(0x00CC7, 0x00CC8 + 1),
    *range(0x00CCA, 0x00CCB + 1),
    *range(0x00D4A, 0x00D4C + 1),
           0x00DDA,
    *range(0x00DDC, 0x00DDE + 1),
           0x00F43,
           0x00F4D,
           0x00F52,
           0x00F57,
           0x00F5C,
           0x00F69,
           0x00F73,
    *range(0x00F75, 0x00F76 + 1),
           0x00F78,
           0x00F81,
           0x00F93,
           0x00F9D,
           0x00FA2,
           0x00FA7,
           0x00FAC,
           0x00FB9,
           0x01026,
           0x01B06,
           0x01B08,
           0x01B0A,
           0x01B0C,
           0x01B0E,
           0x01B12,
           0x01B3B,
           0x01B3D,
    *range(0x01B40, 0x01B41 + 1),
           0x01B43,
    *range(0x01E00, 0x01E99 + 1),
           0x01E9B,
    *range(0x01EA0, 0x01EF9 + 1),
    *range(0x01F00, 0x01F15 + 1),
    *range(0x01F18, 0x01F1D + 1),
    *range(0x01F20, 0x01F45 + 1),
    *range(0x01F48, 0x01F4D + 1),
    *range(0x01F50, 0x01F57 + 1),
           0x01F59,
           0x01F5B,
           0x01F5D,
    *range(0x01F5F, 0x01F7D + 1),
    *range(0x01F80, 0x01FB4 + 1),
    *range(0x01FB6, 0x01FBC + 1),
           0x01FBE,
           0x01FC1,
    *range(0x01FC2, 0x01FC4 + 1),
    *range(0x01FC6, 0x01FCC + 1),
    *range(0x01FCD, 0x01FCF + 1),
    *range(0x01FD0, 0x01FD3 + 1),
    *range(0x01FD6, 0x01FDB + 1),
    *range(0x01FDD, 0x01FDF + 1),
    *range(0x01FE0, 0x01FEC + 1),
    *range(0x01FED, 0x01FEF + 1),
    *range(0x01FF2, 0x01FF4 + 1),
    *range(0x01FF6, 0x01FFC + 1),
           0x01FFD,
    *range(0x02000, 0x02001 + 1),
           0x02126,
    *range(0x0212A, 0x0212B + 1),
    *range(0x0219A, 0x0219B + 1),
           0x021AE,
           0x021CD,
    *range(0x021CE, 0x021CF + 1),
           0x02204,
           0x02209,
           0x0220C,
           0x02224,
           0x02226,
           0x02241,
           0x02244,
           0x02247,
           0x02249,
           0x02260,
           0x02262,
    *range(0x0226D, 0x02271 + 1),
    *range(0x02274, 0x02275 + 1),
    *range(0x02278, 0x02279 + 1),
    *range(0x02280, 0x02281 + 1),
    *range(0x02284, 0x02285 + 1),
    *range(0x02288, 0x02289 + 1),
    *range(0x022AC, 0x022AF + 1),
    *range(0x022E0, 0x022E3 + 1),
    *range(0x022EA, 0x022ED + 1),
           0x02329,
           0x0232A,
           0x02ADC,
           0x0304C,
           0x0304E,
           0x03050,
           0x03052,
           0x03054,
           0x03056,
           0x03058,
           0x0305A,
           0x0305C,
           0x0305E,
           0x03060,
           0x03062,
           0x03065,
           0x03067,
           0x03069,
    *range(0x03070, 0x03071 + 1),
    *range(0x03073, 0x03074 + 1),
    *range(0x03076, 0x03077 + 1),
    *range(0x03079, 0x0307A + 1),
    *range(0x0307C, 0x0307D + 1),
           0x03094,
           0x0309E,
           0x030AC,
           0x030AE,
           0x030B0,
           0x030B2,
           0x030B4,
           0x030B6,
           0x030B8,
           0x030BA,
           0x030BC,
           0x030BE,
           0x030C0,
           0x030C2,
           0x030C5,
           0x030C7,
           0x030C9,
    *range(0x030D0, 0x030D1 + 1),
    *range(0x030D3, 0x030D4 + 1),
    *range(0x030D6, 0x030D7 + 1),
    *range(0x030D9, 0x030DA + 1),
    *range(0x030DC, 0x030DD + 1),
           0x030F4,
    *range(0x030F7, 0x030FA + 1),
           0x030FE,
    *range(0x0AC00, 0x0D7A3 + 1),
    *range(0x0F900, 0x0FA0D + 1),
           0x0FA10,
           0x0FA12,
    *range(0x0FA15, 0x0FA1E + 1),
           0x0FA20,
           0x0FA22,
    *range(0x0FA25, 0x0FA26 + 1),
    *range(0x0FA2A, 0x0FA6D + 1),
    *range(0x0FA70, 0x0FAD9 + 1),
           0x0FB1D,
           0x0FB1F,
    *range(0x0FB2A, 0x0FB36 + 1),
    *range(0x0FB38, 0x0FB3C + 1),
           0x0FB3E,
    *range(0x0FB40, 0x0FB41 + 1),
    *range(0x0FB43, 0x0FB44 + 1),
    *range(0x0FB46, 0x0FB4E + 1),
           0x105C9,
           0x105E4,
           0x1109A,
           0x1109C,
           0x110AB,
    *range(0x1112E, 0x1112F + 1),
    *range(0x1134B, 0x1134C + 1),
           0x11383,
           0x11385,
           0x1138E,
           0x11391,
           0x113C5,
    *range(0x113C7, 0x113C8 + 1),
    *range(0x114BB, 0x114BC + 1),
           0x114BE,
    *range(0x115BA, 0x115BB + 1),
           0x11938,
    *range(0x16121, 0x16128 + 1),
    *range(0x16D68, 0x16D6A + 1),
    *range(0x1D15E, 0x1D164 + 1),
    *range(0x1D1BB, 0x1D1C0 + 1),
    *range(0x2F800, 0x2FA1D + 1),
])

# NFKC_Quick_Check=No
# Characters that cannot ever occur in the normalization form KC
_NFKC_QC_NO = set([
           0x000A0,
           0x000A8,
           0x000AA,
           0x000AF,
    *range(0x000B2, 0x000B3 + 1),
           0x000B4,
           0x000B5,
           0x000B8,
           0x000B9,
           0x000BA,
    *range(0x000BC, 0x000BE + 1),
    *range(0x00132, 0x00133 + 1),
    *range(0x0013F, 0x00140 + 1),
           0x00149,
           0x0017F,
    *range(0x001C4, 0x001CC + 1),
    *range(0x001F1, 0x001F3 + 1),
    *range(0x002B0, 0x002B8 + 1),
    *range(0x002D8, 0x002DD + 1),
    *range(0x002E0, 0x002E4 + 1),
    *range(0x00340, 0x00341 + 1),
    *range(0x00343, 0x00344 + 1),
           0x00374,
           0x0037A,
           0x0037E,
    *range(0x00384, 0x00385 + 1),
           0x00387,
    *range(0x003D0, 0x003D6 + 1),
    *range(0x003F0, 0x003F2 + 1),
    *range(0x003F4, 0x003F5 + 1),
           0x003F9,
           0x00587,
    *range(0x00675, 0x00678 + 1),
    *range(0x00958, 0x0095F + 1),
    *range(0x009DC, 0x009DD + 1),
           0x009DF,
           0x00A33,
           0x00A36,
    *range(0x00A59, 0x00A5B + 1),
           0x00A5E,
    *range(0x00B5C, 0x00B5D + 1),
           0x00E33,
           0x00EB3,
    *range(0x00EDC, 0x00EDD + 1),
           0x00F0C,
           0x00F43,
           0x00F4D,
           0x00F52,
           0x00F57,
           0x00F5C,
           0x00F69,
           0x00F73,
    *range(0x00F75, 0x00F79 + 1),
           0x00F81,
           0x00F93,
           0x00F9D,
           0x00FA2,
           0x00FA7,
           0x00FAC,
           0x00FB9,
           0x010FC,
    *range(0x01D2C, 0x01D2E + 1),
    *range(0x01D30, 0x01D3A + 1),
    *range(0x01D3C, 0x01D4D + 1),
    *range(0x01D4F, 0x01D6A + 1),
           0x01D78,
    *range(0x01D9B, 0x01DBF + 1),
    *range(0x01E9A, 0x01E9B + 1),
           0x01F71,
           0x01F73,
           0x01F75,
           0x01F77,
           0x01F79,
           0x01F7B,
           0x01F7D,
           0x01FBB,
           0x01FBD,
           0x01FBE,
    *range(0x01FBF, 0x01FC1 + 1),
           0x01FC9,
           0x01FCB,
    *range(0x01FCD, 0x01FCF + 1),
           0x01FD3,
           0x01FDB,
    *range(0x01FDD, 0x01FDF + 1),
           0x01FE3,
           0x01FEB,
    *range(0x01FED, 0x01FEF + 1),
           0x01FF9,
           0x01FFB,
    *range(0x01FFD, 0x01FFE + 1),
    *range(0x02000, 0x0200A + 1),
           0x02011,
           0x02017,
    *range(0x02024, 0x02026 + 1),
           0x0202F,
    *range(0x02033, 0x02034 + 1),
    *range(0x02036, 0x02037 + 1),
           0x0203C,
           0x0203E,
    *range(0x02047, 0x02049 + 1),
           0x02057,
           0x0205F,
           0x02070,
           0x02071,
    *range(0x02074, 0x02079 + 1),
    *range(0x0207A, 0x0207C + 1),
           0x0207D,
           0x0207E,
           0x0207F,
    *range(0x02080, 0x02089 + 1),
    *range(0x0208A, 0x0208C + 1),
           0x0208D,
           0x0208E,
    *range(0x02090, 0x0209C + 1),
           0x020A8,
    *range(0x02100, 0x02101 + 1),
           0x02102,
           0x02103,
    *range(0x02105, 0x02106 + 1),
           0x02107,
           0x02109,
    *range(0x0210A, 0x02113 + 1),
           0x02115,
           0x02116,
    *range(0x02119, 0x0211D + 1),
    *range(0x02120, 0x02122 + 1),
           0x02124,
           0x02126,
           0x02128,
    *range(0x0212A, 0x0212D + 1),
    *range(0x0212F, 0x02131 + 1),
    *range(0x02133, 0x02134 + 1),
    *range(0x02135, 0x02138 + 1),
           0x02139,
           0x0213B,
    *range(0x0213C, 0x0213F + 1),
           0x02140,
    *range(0x02145, 0x02149 + 1),
    *range(0x02150, 0x0215F + 1),
    *range(0x02160, 0x0217F + 1),
           0x02189,
    *range(0x0222C, 0x0222D + 1),
    *range(0x0222F, 0x02230 + 1),
           0x02329,
           0x0232A,
    *range(0x02460, 0x0249B + 1),
    *range(0x0249C, 0x024E9 + 1),
           0x024EA,
           0x02A0C,
    *range(0x02A74, 0x02A76 + 1),
           0x02ADC,
    *range(0x02C7C, 0x02C7D + 1),
           0x02D6F,
           0x02E9F,
           0x02EF3,
    *range(0x02F00, 0x02FD5 + 1),
           0x03000,
           0x03036,
    *range(0x03038, 0x0303A + 1),
    *range(0x0309B, 0x0309C + 1),
           0x0309F,
           0x030FF,
    *range(0x03131, 0x0318E + 1),
    *range(0x03192, 0x03195 + 1),
    *range(0x03196, 0x0319F + 1),
    *range(0x03200, 0x0321E + 1),
    *range(0x03220, 0x03229 + 1),
    *range(0x0322A, 0x03247 + 1),
           0x03250,
    *range(0x03251, 0x0325F + 1),
    *range(0x03260, 0x0327E + 1),
    *range(0x03280, 0x03289 + 1),
    *range(0x0328A, 0x032B0 + 1),
    *range(0x032B1, 0x032BF + 1),
    *range(0x032C0, 0x033FF + 1),
    *range(0x0A69C, 0x0A69D + 1),
           0x0A770,
    *range(0x0A7F2, 0x0A7F4 + 1),
    *range(0x0A7F8, 0x0A7F9 + 1),
    *range(0x0AB5C, 0x0AB5F + 1),
           0x0AB69,
    *range(0x0F900, 0x0FA0D + 1),
           0x0FA10,
           0x0FA12,
    *range(0x0FA15, 0x0FA1E + 1),
           0x0FA20,
           0x0FA22,
    *range(0x0FA25, 0x0FA26 + 1),
    *range(0x0FA2A, 0x0FA6D + 1),
    *range(0x0FA70, 0x0FAD9 + 1),
    *range(0x0FB00, 0x0FB06 + 1),
    *range(0x0FB13, 0x0FB17 + 1),
           0x0FB1D,
    *range(0x0FB1F, 0x0FB28 + 1),
           0x0FB29,
    *range(0x0FB2A, 0x0FB36 + 1),
    *range(0x0FB38, 0x0FB3C + 1),
           0x0FB3E,
    *range(0x0FB40, 0x0FB41 + 1),
    *range(0x0FB43, 0x0FB44 + 1),
    *range(0x0FB46, 0x0FBB1 + 1),
    *range(0x0FBD3, 0x0FD3D + 1),
    *range(0x0FD50, 0x0FD8F + 1),
    *range(0x0FD92, 0x0FDC7 + 1),
    *range(0x0FDF0, 0x0FDFB + 1),
           0x0FDFC,
    *range(0x0FE10, 0x0FE16 + 1),
           0x0FE17,
           0x0FE18,
           0x0FE19,
           0x0FE30,
    *range(0x0FE31, 0x0FE32 + 1),
    *range(0x0FE33, 0x0FE34 + 1),
           0x0FE35,
           0x0FE36,
           0x0FE37,
           0x0FE38,
           0x0FE39,
           0x0FE3A,
           0x0FE3B,
           0x0FE3C,
           0x0FE3D,
           0x0FE3E,
           0x0FE3F,
           0x0FE40,
           0x0FE41,
           0x0FE42,
           0x0FE43,
           0x0FE44,
           0x0FE47,
           0x0FE48,
    *range(0x0FE49, 0x0FE4C + 1),
    *range(0x0FE4D, 0x0FE4F + 1),
    *range(0x0FE50, 0x0FE52 + 1),
    *range(0x0FE54, 0x0FE57 + 1),
           0x0FE58,
           0x0FE59,
           0x0FE5A,
           0x0FE5B,
           0x0FE5C,
           0x0FE5D,
           0x0FE5E,
    *range(0x0FE5F, 0x0FE61 + 1),
           0x0FE62,
           0x0FE63,
    *range(0x0FE64, 0x0FE66 + 1),
           0x0FE68,
           0x0FE69,
    *range(0x0FE6A, 0x0FE6B + 1),
    *range(0x0FE70, 0x0FE72 + 1),
           0x0FE74,
    *range(0x0FE76, 0x0FEFC + 1),
    *range(0x0FF01, 0x0FF03 + 1),
           0x0FF04,
    *range(0x0FF05, 0x0FF07 + 1),
           0x0FF08,
           0x0FF09,
           0x0FF0A,
           0x0FF0B,
           0x0FF0C,
           0x0FF0D,
    *range(0x0FF0E, 0x0FF0F + 1),
    *range(0x0FF10, 0x0FF19 + 1),
    *range(0x0FF1A, 0x0FF1B + 1),
    *range(0x0FF1C, 0x0FF1E + 1),
    *range(0x0FF1F, 0x0FF20 + 1),
    *range(0x0FF21, 0x0FF3A + 1),
           0x0FF3B,
           0x0FF3C,
           0x0FF3D,
           0x0FF3E,
           0x0FF3F,
           0x0FF40,
    *range(0x0FF41, 0x0FF5A + 1),
           0x0FF5B,
           0x0FF5C,
           0x0FF5D,
           0x0FF5E,
           0x0FF5F,
           0x0FF60,
           0x0FF61,
           0x0FF62,
           0x0FF63,
    *range(0x0FF64, 0x0FF65 + 1),
    *range(0x0FF66, 0x0FF6F + 1),
           0x0FF70,
    *range(0x0FF71, 0x0FF9D + 1),
    *range(0x0FF9E, 0x0FF9F + 1),
    *range(0x0FFA0, 0x0FFBE + 1),
    *range(0x0FFC2, 0x0FFC7 + 1),
    *range(0x0FFCA, 0x0FFCF + 1),
    *range(0x0FFD2, 0x0FFD7 + 1),
    *range(0x0FFDA, 0x0FFDC + 1),
    *range(0x0FFE0, 0x0FFE1 + 1),
           0x0FFE2,
           0x0FFE3,
           0x0FFE4,
    *range(0x0FFE5, 0x0FFE6 + 1),
           0x0FFE8,
    *range(0x0FFE9, 0x0FFEC + 1),
    *range(0x0FFED, 0x0FFEE + 1),
    *range(0x10781, 0x10785 + 1),
    *range(0x10787, 0x107B0 + 1),
    *range(0x107B2, 0x107BA + 1),
    *range(0x1CCD6, 0x1CCEF + 1),
    *range(0x1CCF0, 0x1CCF9 + 1),
    *range(0x1D15E, 0x1D164 + 1),
    *range(0x1D1BB, 0x1D1C0 + 1),
    *range(0x1D400, 0x1D454 + 1),
    *range(0x1D456, 0x1D49C + 1),
    *range(0x1D49E, 0x1D49F + 1),
           0x1D4A2,
    *range(0x1D4A5, 0x1D4A6 + 1),
    *range(0x1D4A9, 0x1D4AC + 1),
    *range(0x1D4AE, 0x1D4B9 + 1),
           0x1D4BB,
    *range(0x1D4BD, 0x1D4C3 + 1),
    *range(0x1D4C5, 0x1D505 + 1),
    *range(0x1D507, 0x1D50A + 1),
    *range(0x1D50D, 0x1D514 + 1),
    *range(0x1D516, 0x1D51C + 1),
    *range(0x1D51E, 0x1D539 + 1),
    *range(0x1D53B, 0x1D53E + 1),
    *range(0x1D540, 0x1D544 + 1),
           0x1D546,
    *range(0x1D54A, 0x1D550 + 1),
    *range(0x1D552, 0x1D6A5 + 1),
    *range(0x1D6A8, 0x1D6C0 + 1),
           0x1D6C1,
    *range(0x1D6C2, 0x1D6DA + 1),
           0x1D6DB,
    *range(0x1D6DC, 0x1D6FA + 1),
           0x1D6FB,
    *range(0x1D6FC, 0x1D714 + 1),
           0x1D715,
    *range(0x1D716, 0x1D734 + 1),
           0x1D735,
    *range(0x1D736, 0x1D74E + 1),
           0x1D74F,
    *range(0x1D750, 0x1D76E + 1),
           0x1D76F,
    *range(0x1D770, 0x1D788 + 1),
           0x1D789,
    *range(0x1D78A, 0x1D7A8 + 1),
           0x1D7A9,
    *range(0x1D7AA, 0x1D7C2 + 1),
           0x1D7C3,
    *range(0x1D7C4, 0x1D7CB + 1),
    *range(0x1D7CE, 0x1D7FF + 1),
    *range(0x1E030, 0x1E06D + 1),
    *range(0x1EE00, 0x1EE03 + 1),
    *range(0x1EE05, 0x1EE1F + 1),
    *range(0x1EE21, 0x1EE22 + 1),
           0x1EE24,
           0x1EE27,
    *range(0x1EE29, 0x1EE32 + 1),
    *range(0x1EE34, 0x1EE37 + 1),
           0x1EE39,
           0x1EE3B,
           0x1EE42,
           0x1EE47,
           0x1EE49,
           0x1EE4B,
    *range(0x1EE4D, 0x1EE4F + 1),
    *range(0x1EE51, 0x1EE52 + 1),
           0x1EE54,
           0x1EE57,
           0x1EE59,
           0x1EE5B,
           0x1EE5D,
           0x1EE5F,
    *range(0x1EE61, 0x1EE62 + 1),
           0x1EE64,
    *range(0x1EE67, 0x1EE6A + 1),
    *range(0x1EE6C, 0x1EE72 + 1),
    *range(0x1EE74, 0x1EE77 + 1),
    *range(0x1EE79, 0x1EE7C + 1),
           0x1EE7E,
    *range(0x1EE80, 0x1EE89 + 1),
    *range(0x1EE8B, 0x1EE9B + 1),
    *range(0x1EEA1, 0x1EEA3 + 1),
    *range(0x1EEA5, 0x1EEA9 + 1),
    *range(0x1EEAB, 0x1EEBB + 1),
    *range(0x1F100, 0x1F10A + 1),
    *range(0x1F110, 0x1F12E + 1),
    *range(0x1F130, 0x1F14F + 1),
    *range(0x1F16A, 0x1F16C + 1),
           0x1F190,
    *range(0x1F200, 0x1F202 + 1),
    *range(0x1F210, 0x1F23B + 1),
    *range(0x1F240, 0x1F248 + 1),
    *range(0x1F250, 0x1F251 + 1),
    *range(0x1FBF0, 0x1FBF9 + 1),
    *range(0x2F800, 0x2FA1D + 1),
])

# NFKC_Quick_Check=Maybe
# Characters that may or may not occur in the normalization form KC,
# depending on the context
_NFKC_QC_MAYBE = set([
    *range(0x00300, 0x00304 + 1),
    *range(0x00306, 0x0030C + 1),
           0x0030F,
           0x00311,
    *range(0x00313, 0x00314 + 1),
           0x0031B,
    *range(0x00323, 0x00328 + 1),
    *range(0x0032D, 0x0032E + 1),
    *range(0x00330, 0x00331 + 1),
           0x00338,
           0x00342,
           0x00345,
    *range(0x00653, 0x00655 + 1),
           0x0093C,
           0x009BE,
           0x009D7,
           0x00B3E,
           0x00B56,
           0x00B57,
           0x00BBE,
           0x00BD7,
           0x00C56,
           0x00CC2,
    *range(0x00CD5, 0x00CD6 + 1),
           0x00D3E,
           0x00D57,
           0x00DCA,
           0x00DCF,
           0x00DDF,
           0x0102E,
    *range(0x01161, 0x01175 + 1),
    *range(0x011A8, 0x011C2 + 1),
           0x01B35,
    *range(0x03099, 0x0309A + 1),
           0x110BA,
           0x11127,
           0x1133E,
           0x11357,
           0x113B8,
           0x113BB,
           0x113C2,
           0x113C5,
    *range(0x113C7, 0x113C9 + 1),
           0x114B0,
           0x114BA,
           0x114BD,
           0x115AF,
           0x11930,
    *range(0x1611E, 0x16129 + 1),
    *range(0x16D67, 0x16D68 + 1),
])

# Code points listed for NFKC_Quick_Check=No or NFKC_Quick_Check=Maybe
_NFKC_QC_NO_OR_MAYBE = _NFKC_QC_NO | _NFKC_QC_MAYBE

# NFKD_Quick_Check=No
# Characters that cannot ever occur in the normalization form KD
_NFKD_QC_NO = set([
           0x000A0,
           0x000A8,
           0x000AA,
           0x000AF,
    *range(0x000B2, 0x000B3 + 1),
           0x000B4,
           0x000B5,
           0x000B8,
           0x000B9,
           0x000BA,
    *range(0x000BC, 0x000BE + 1),
    *range(0x000C0, 0x000C5 + 1),
    *range(0x000C7, 0x000CF + 1),
    *range(0x000D1, 0x000D6 + 1),
    *range(0x000D9, 0x000DD + 1),
    *range(0x000E0, 0x000E5 + 1),
    *range(0x000E7, 0x000EF + 1),
    *range(0x000F1, 0x000F6 + 1),
    *range(0x000F9, 0x000FD + 1),
    *range(0x000FF, 0x0010F + 1),
    *range(0x00112, 0x00125 + 1),
    *range(0x00128, 0x00130 + 1),
    *range(0x00132, 0x00137 + 1),
    *range(0x00139, 0x00140 + 1),
    *range(0x00143, 0x00149 + 1),
    *range(0x0014C, 0x00151 + 1),
    *range(0x00154, 0x00165 + 1),
    *range(0x00168, 0x0017F + 1),
    *range(0x001A0, 0x001A1 + 1),
    *range(0x001AF, 0x001B0 + 1),
    *range(0x001C4, 0x001DC + 1),
    *range(0x001DE, 0x001E3 + 1),
    *range(0x001E6, 0x001F5 + 1),
    *range(0x001F8, 0x0021B + 1),
    *range(0x0021E, 0x0021F + 1),
    *range(0x00226, 0x00233 + 1),
    *range(0x002B0, 0x002B8 + 1),
    *range(0x002D8, 0x002DD + 1),
    *range(0x002E0, 0x002E4 + 1),
    *range(0x00340, 0x00341 + 1),
    *range(0x00343, 0x00344 + 1),
           0x00374,
           0x0037A,
           0x0037E,
    *range(0x00384, 0x00385 + 1),
           0x00386,
           0x00387,
    *range(0x00388, 0x0038A + 1),
           0x0038C,
    *range(0x0038E, 0x00390 + 1),
    *range(0x003AA, 0x003B0 + 1),
    *range(0x003CA, 0x003CE + 1),
    *range(0x003D0, 0x003D6 + 1),
    *range(0x003F0, 0x003F2 + 1),
    *range(0x003F4, 0x003F5 + 1),
           0x003F9,
    *range(0x00400, 0x00401 + 1),
           0x00403,
           0x00407,
    *range(0x0040C, 0x0040E + 1),
           0x00419,
           0x00439,
    *range(0x00450, 0x00451 + 1),
           0x00453,
           0x00457,
    *range(0x0045C, 0x0045E + 1),
    *range(0x00476, 0x00477 + 1),
    *range(0x004C1, 0x004C2 + 1),
    *range(0x004D0, 0x004D3 + 1),
    *range(0x004D6, 0x004D7 + 1),
    *range(0x004DA, 0x004DF + 1),
    *range(0x004E2, 0x004E7 + 1),
    *range(0x004EA, 0x004F5 + 1),
    *range(0x004F8, 0x004F9 + 1),
           0x00587,
    *range(0x00622, 0x00626 + 1),
    *range(0x00675, 0x00678 + 1),
           0x006C0,
           0x006C2,
           0x006D3,
           0x00929,
           0x00931,
           0x00934,
    *range(0x00958, 0x0095F + 1),
    *range(0x009CB, 0x009CC + 1),
    *range(0x009DC, 0x009DD + 1),
           0x009DF,
           0x00A33,
           0x00A36,
    *range(0x00A59, 0x00A5B + 1),
           0x00A5E,
           0x00B48,
    *range(0x00B4B, 0x00B4C + 1),
    *range(0x00B5C, 0x00B5D + 1),
           0x00B94,
    *range(0x00BCA, 0x00BCC + 1),
           0x00C48,
           0x00CC0,
    *range(0x00CC7, 0x00CC8 + 1),
    *range(0x00CCA, 0x00CCB + 1),
    *range(0x00D4A, 0x00D4C + 1),
           0x00DDA,
    *range(0x00DDC, 0x00DDE + 1),
           0x00E33,
           0x00EB3,
    *range(0x00EDC, 0x00EDD + 1),
           0x00F0C,
           0x00F43,
           0x00F4D,
           0x00F52,
           0x00F57,
           0x00F5C,
           0x00F69,
           0x00F73,
    *range(0x00F75, 0x00F79 + 1),
           0x00F81,
           0x00F93,
           0x00F9D,
           0x00FA2,
           0x00FA7,
           0x00FAC,
           0x00FB9,
           0x01026,
           0x010FC,
           0x01B06,
           0x01B08,
           0x01B0A,
           0x01B0C,
           0x01B0E,
           0x01B12,
           0x01B3B,
           0x01B3D,
    *range(0x01B40, 0x01B41 + 1),
           0x01B43,
    *range(0x01D2C, 0x01D2E + 1),
    *range(0x01D30, 0x01D3A + 1),
    *range(0x01D3C, 0x01D4D + 1),
    *range(0x01D4F, 0x01D6A + 1),
           0x01D78,
    *range(0x01D9B, 0x01DBF + 1),
    *range(0x01E00, 0x01E9B + 1),
    *range(0x01EA0, 0x01EF9 + 1),
    *range(0x01F00, 0x01F15 + 1),
    *range(0x01F18, 0x01F1D + 1),
    *range(0x01F20, 0x01F45 + 1),
    *range(0x01F48, 0x01F4D + 1),
    *range(0x01F50, 0x01F57 + 1),
           0x01F59,
           0x01F5B,
           0x01F5D,
    *range(0x01F5F, 0x01F7D + 1),
    *range(0x01F80, 0x01FB4 + 1),
    *range(0x01FB6, 0x01FBC + 1),
           0x01FBD,
           0x01FBE,
    *range(0x01FBF, 0x01FC1 + 1),
    *range(0x01FC2, 0x01FC4 + 1),
    *range(0x01FC6, 0x01FCC + 1),
    *range(0x01FCD, 0x01FCF + 1),
    *range(0x01FD0, 0x01FD3 + 1),
    *range(0x01FD6, 0x01FDB + 1),
    *range(0x01FDD, 0x01FDF + 1),
    *range(0x01FE0, 0x01FEC + 1),
    *range(0x01FED, 0x01FEF + 1),
    *range(0x01FF2, 0x01FF4 + 1),
    *range(0x01FF6, 0x01FFC + 1),
    *range(0x01FFD, 0x01FFE + 1),
    *range(0x02000, 0x0200A + 1),
           0x02011,
           0x02017,
    *range(0x02024, 0x02026 + 1),
           0x0202F,
    *range(0x02033, 0x02034 + 1),
    *range(0x02036, 0x02037 + 1),
           0x0203C,
           0x0203E,
    *range(0x02047, 0x02049 + 1),
           0x02057,
           0x0205F,
           0x02070,
           0x02071,
    *range(0x02074, 0x02079 + 1),
    *range(0x0207A, 0x0207C + 1),
           0x0207D,
           0x0207E,
           0x0207F,
    *range(0x02080, 0x02089 + 1),
    *range(0x0208A, 0x0208C + 1),
           0x0208D,
           0x0208E,
    *range(0x02090, 0x0209C + 1),
           0x020A8,
    *range(0x02100, 0x02101 + 1),
           0x02102,
           0x02103,
    *range(0x02105, 0x02106 + 1),
           0x02107,
           0x02109,
    *range(0x0210A, 0x02113 + 1),
           0x02115,
           0x02116,
    *range(0x02119, 0x0211D + 1),
    *range(0x02120, 0x02122 + 1),
           0x02124,
           0x02126,
           0x02128,
    *range(0x0212A, 0x0212D + 1),
    *range(0x0212F, 0x02131 + 1),
    *range(0x02133, 0x02134 + 1),
    *range(0x02135, 0x02138 + 1),
           0x02139,
           0x0213B,
    *range(0x0213C, 0x0213F + 1),
           0x02140,
    *range(0x02145, 0x02149 + 1),
    *range(0x02150, 0x0215F + 1),
    *range(0x02160, 0x0217F + 1),
           0x02189,
    *range(0x0219A, 0x0219B + 1),
           0x021AE,
           0x021CD,
    *range(0x021CE, 0x021CF + 1),
           0x02204,
           0x02209,
           0x0220C,
           0x02224,
           0x02226,
    *range(0x0222C, 0x0222D + 1),
    *range(0x0222F, 0x02230 + 1),
           0x02241,
           0x02244,
           0x02247,
           0x02249,
           0x02260,
           0x02262,
    *range(0x0226D, 0x02271 + 1),
    *range(0x02274, 0x02275 + 1),
    *range(0x02278, 0x02279 + 1),
    *range(0x02280, 0x02281 + 1),
    *range(0x02284, 0x02285 + 1),
    *range(0x02288, 0x02289 + 1),
    *range(0x022AC, 0x022AF + 1),
    *range(0x022E0, 0x022E3 + 1),
    *range(0x022EA, 0x022ED + 1),
           0x02329,
           0x0232A,
    *range(0x02460, 0x0249B + 1),
    *range(0x0249C, 0x024E9 + 1),
           0x024EA,
           0x02A0C,
    *range(0x02A74, 0x02A76 + 1),
           0x02ADC,
    *range(0x02C7C, 0x02C7D + 1),
           0x02D6F,
           0x02E9F,
           0x02EF3,
    *range(0x02F00, 0x02FD5 + 1),
           0x03000,
           0x03036,
    *range(0x03038, 0x0303A + 1),
           0x0304C,
           0x0304E,
           0x03050,
           0x03052,
           0x03054,
           0x03056,
           0x03058,
           0x0305A,
           0x0305C,
           0x0305E,
           0x03060,
           0x03062,
           0x03065,
           0x03067,
           0x03069,
    *range(0x03070, 0x03071 + 1),
    *range(0x03073, 0x03074 + 1),
    *range(0x03076, 0x03077 + 1),
    *range(0x03079, 0x0307A + 1),
    *range(0x0307C, 0x0307D + 1),
           0x03094,
    *range(0x0309B, 0x0309C + 1),
           0x0309E,
           0x0309F,
           0x030AC,
           0x030AE,
           0x030B0,
           0x030B2,
           0x030B4,
           0x030B6,
           0x030B8,
           0x030BA,
           0x030BC,
           0x030BE,
           0x030C0,
           0x030C2,
           0x030C5,
           0x030C7,
           0x030C9,
    *range(0x030D0, 0x030D1 + 1),
    *range(0x030D3, 0x030D4 + 1),
    *range(0x030D6, 0x030D7 + 1),
    *range(0x030D9, 0x030DA + 1),
    *range(0x030DC, 0x030DD + 1),
           0x030F4,
    *range(0x030F7, 0x030FA + 1),
           0x030FE,
           0x030FF,
    *range(0x03131, 0x0318E + 1),
    *range(0x03192, 0x03195 + 1),
    *range(0x03196, 0x0319F + 1),
    *range(0x03200, 0x0321E + 1),
    *range(0x03220, 0x03229 + 1),
    *range(0x0322A, 0x03247 + 1),
           0x03250,
    *range(0x03251, 0x0325F + 1),
    *range(0x03260, 0x0327E + 1),
    *range(0x03280, 0x03289 + 1),
    *range(0x0328A, 0x032B0 + 1),
    *range(0x032B1, 0x032BF + 1),
    *range(0x032C0, 0x033FF + 1),
    *range(0x0A69C, 0x0A69D + 1),
           0x0A770,
    *range(0x0A7F2, 0x0A7F4 + 1),
    *range(0x0A7F8, 0x0A7F9 + 1),
    *range(0x0AB5C, 0x0AB5F + 1),
           0x0AB69,
    *range(0x0AC00, 0x0D7A3 + 1),
    *range(0x0F900, 0x0FA0D + 1),
           0x0FA10,
           0x0FA12,
    *range(0x0FA15, 0x0FA1E + 1),
           0x0FA20,
           0x0FA22,
    *range(0x0FA25, 0x0FA26 + 1),
    *range(0x0FA2A, 0x0FA6D + 1),
    *range(0x0FA70, 0x0FAD9 + 1),
    *range(0x0FB00, 0x0FB06 + 1),
    *range(0x0FB13, 0x0FB17 + 1),
           0x0FB1D,
    *range(0x0FB1F, 0x0FB28 + 1),
           0x0FB29,
    *range(0x0FB2A, 0x0FB36 + 1),
    *range(0x0FB38, 0x0FB3C + 1),
           0x0FB3E,
    *range(0x0FB40, 0x0FB41 + 1),
    *range(0x0FB43, 0x0FB44 + 1),
    *range(0x0FB46, 0x0FBB1 + 1),
    *range(0x0FBD3, 0x0FD3D + 1),
    *range(0x0FD50, 0x0FD8F + 1),
    *range(0x0FD92, 0x0FDC7 + 1),
    *range(0x0FDF0, 0x0FDFB + 1),
           0x0FDFC,
    *range(0x0FE10, 0x0FE16 + 1),
           0x0FE17,
           0x0FE18,
           0x0FE19,
           0x0FE30,
    *range(0x0FE31, 0x0FE32 + 1),
    *range(0x0FE33, 0x0FE34 + 1),
           0x0FE35,
           0x0FE36,
           0x0FE37,
           0x0FE38,
           0x0FE39,
           0x0FE3A,
           0x0FE3B,
           0x0FE3C,
           0x0FE3D,
           0x0FE3E,
           0x0FE3F,
           0x0FE40,
           0x0FE41,
           0x0FE42,
           0x0FE43,
           0x0FE44,
           0x0FE47,
           0x0FE48,
    *range(0x0FE49, 0x0FE4C + 1),
    *range(0x0FE4D, 0x0FE4F + 1),
    *range(0x0FE50, 0x0FE52 + 1),
    *range(0x0FE54, 0x0FE57 + 1),
           0x0FE58,
           0x0FE59,
           0x0FE5A,
           0x0FE5B,
           0x0FE5C,
           0x0FE5D,
           0x0FE5E,
    *range(0x0FE5F, 0x0FE61 + 1),
           0x0FE62,
           0x0FE63,
    *range(0x0FE64, 0x0FE66 + 1),
           0x0FE68,
           0x0FE69,
    *range(0x0FE6A, 0x0FE6B + 1),
    *range(0x0FE70, 0x0FE72 + 1),
           0x0FE74,
    *range(0x0FE76, 0x0FEFC + 1),
    *range(0x0FF01, 0x0FF03 + 1),
           0x0FF04,
    *range(0x0FF05, 0x0FF07 + 1),
           0x0FF08,
           0x0FF09,
           0x0FF0A,
           0x0FF0B,
           0x0FF0C,
           0x0FF0D,
    *range(0x0FF0E, 0x0FF0F + 1),
    *range(0x0FF10, 0x0FF19 + 1),
    *range(0x0FF1A, 0x0FF1B + 1),
    *range(0x0FF1C, 0x0FF1E + 1),
    *range(0x0FF1F, 0x0FF20 + 1),
    *range(0x0FF21, 0x0FF3A + 1),
           0x0FF3B,
           0x0FF3C,
           0x0FF3D,
           0x0FF3E,
           0x0FF3F,
           0x0FF40,
    *range(0x0FF41, 0x0FF5A + 1),
           0x0FF5B,
           0x0FF5C,
           0x0FF5D,
           0x0FF5E,
           0x0FF5F,
           0x0FF60,
           0x0FF61,
           0x0FF62,
           0x0FF63,
    *range(0x0FF64, 0x0FF65 + 1),
    *range(0x0FF66, 0x0FF6F + 1),
           0x0FF70,
    *range(0x0FF71, 0x0FF9D + 1),
    *range(0x0FF9E, 0x0FF9F + 1),
    *range(0x0FFA0, 0x0FFBE + 1),
    *range(0x0FFC2, 0x0FFC7 + 1),
    *range(0x0FFCA, 0x0FFCF + 1),
    *range(0x0FFD2, 0x0FFD7 + 1),
    *range(0x0FFDA, 0x0FFDC + 1),
    *range(0x0FFE0, 0x0FFE1 + 1),
           0x0FFE2,
           0x0FFE3,
           0x0FFE4,
    *range(0x0FFE5, 0x0FFE6 + 1),
           0x0FFE8,
    *range(0x0FFE9, 0x0FFEC + 1),
    *range(0x0FFED, 0x0FFEE + 1),
           0x105C9,
           0x105E4,
    *range(0x10781, 0x10785 + 1),
    *range(0x10787, 0x107B0 + 1),
    *range(0x107B2, 0x107BA + 1),
           0x1109A,
           0x1109C,
           0x110AB,
    *range(0x1112E, 0x1112F + 1),
    *range(0x1134B, 0x1134C + 1),
           0x11383,
           0x11385,
           0x1138E,
           0x11391,
           0x113C5,
    *range(0x113C7, 0x113C8 + 1),
    *range(0x114BB, 0x114BC + 1),
           0x114BE,
    *range(0x115BA, 0x115BB + 1),
           0x11938,
    *range(0x16121, 0x16128 + 1),
    *range(0x16D68, 0x16D6A + 1),
    *range(0x1CCD6, 0x1CCEF + 1),
    *range(0x1CCF0, 0x1CCF9 + 1),
    *range(0x1D15E, 0x1D164 + 1),
    *range(0x1D1BB, 0x1D1C0 + 1),
    *range(0x1D400, 0x1D454 + 1),
    *range(0x1D456, 0x1D49C + 1),
    *range(0x1D49E, 0x1D49F + 1),
           0x1D4A2,
    *range(0x1D4A5, 0x1D4A6 + 1),
    *range(0x1D4A9, 0x1D4AC + 1),
    *range(0x1D4AE, 0x1D4B9 + 1),
           0x1D4BB,
    *range(0x1D4BD, 0x1D4C3 + 1),
    *range(0x1D4C5, 0x1D505 + 1),
    *range(0x1D507, 0x1D50A + 1),
    *range(0x1D50D, 0x1D514 + 1),
    *range(0x1D516, 0x1D51C + 1),
    *range(0x1D51E, 0x1D539 + 1),
    *range(0x1D53B, 0x1D53E + 1),
    *range(0x1D540, 0x1D544 + 1),
           0x1D546,
    *range(0x1D54A, 0x1D550 + 1),
    *range(0x1D552, 0x1D6A5 + 1),
    *range(0x1D6A8, 0x1D6C0 + 1),
           0x1D6C1,
    *range(0x1D6C2, 0x1D6DA + 1),
           0x1D6DB,
    *range(0x1D6DC, 0x1D6FA + 1),
           0x1D6FB,
    *range(0x1D6FC, 0x1D714 + 1),
           0x1D715,
    *range(0x1D716, 0x1D734 + 1),
           0x1D735,
    *range(0x1D736, 0x1D74E + 1),
           0x1D74F,
    *range(0x1D750, 0x1D76E + 1),
           0x1D76F,
    *range(0x1D770, 0x1D788 + 1),
           0x1D789,
    *range(0x1D78A, 0x1D7A8 + 1),
           0x1D7A9,
    *range(0x1D7AA, 0x1D7C2 + 1),
           0x1D7C3,
    *range(0x1D7C4, 0x1D7CB + 1),
    *range(0x1D7CE, 0x1D7FF + 1),
    *range(0x1E030, 0x1E06D + 1),
    *range(0x1EE00, 0x1EE03 + 1),
    *range(0x1EE05, 0x1EE1F + 1),
    *range(0x1EE21, 0x1EE22 + 1),
           0x1EE24,
           0x1EE27,
    *range(0x1EE29, 0x1EE32 + 1),
    *range(0x1EE34, 0x1EE37 + 1),
           0x1EE39,
           0x1EE3B,
           0x1EE42,
           0x1EE47,
           0x1EE49,
           0x1EE4B,
    *range(0x1EE4D, 0x1EE4F + 1),
    *range(0x1EE51, 0x1EE52 + 1),
           0x1EE54,
           0x1EE57,
           0x1EE59,
           0x1EE5B,
           0x1EE5D,
           0x1EE5F,
    *range(0x1EE61, 0x1EE62 + 1),
           0x1EE64,
    *range(0x1EE67, 0x1EE6A + 1),
    *range(0x1EE6C, 0x1EE72 + 1),
    *range(0x1EE74, 0x1EE77 + 1),
    *range(0x1EE79, 0x1EE7C + 1),
           0x1EE7E,
    *range(0x1EE80, 0x1EE89 + 1),
    *range(0x1EE8B, 0x1EE9B + 1),
    *range(0x1EEA1, 0x1EEA3 + 1),
    *range(0x1EEA5, 0x1EEA9 + 1),
    *range(0x1EEAB, 0x1EEBB + 1),
    *range(0x1F100, 0x1F10A + 1),
    *range(0x1F110, 0x1F12E + 1),
    *range(0x1F130, 0x1F14F + 1),
    *range(0x1F16A, 0x1F16C + 1),
           0x1F190,
    *range(0x1F200, 0x1F202 + 1),
    *range(0x1F210, 0x1F23B + 1),
    *range(0x1F240, 0x1F248 + 1),
    *range(0x1F250, 0x1F251 + 1),
    *range(0x1FBF0, 0x1FBF9 + 1),
    *range(0x2F800, 0x2FA1D + 1),
])

del _NFC__QC_NO, _NFC__QC_MAYBE, _NFKC_QC_NO, _NFKC_QC_MAYBE
