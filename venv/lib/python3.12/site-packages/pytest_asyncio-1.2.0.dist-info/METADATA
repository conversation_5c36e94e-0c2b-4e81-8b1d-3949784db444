Metadata-Version: 2.4
Name: pytest-asyncio
Version: 1.2.0
Summary: Pytest support for asyncio
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License-Expression: Apache-2.0
Project-URL: Bug Tracker, https://github.com/pytest-dev/pytest-asyncio/issues
Project-URL: Changelog, https://pytest-asyncio.readthedocs.io/en/latest/reference/changelog.html
Project-URL: Documentation, https://pytest-asyncio.readthedocs.io
Project-URL: Homepage, https://github.com/pytest-dev/pytest-asyncio
Project-URL: Source Code, https://github.com/pytest-dev/pytest-asyncio
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: AsyncIO
Classifier: Framework :: Pytest
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Topic :: Software Development :: Testing
Classifier: Typing :: Typed
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: backports-asyncio-runner<2,>=1.1; python_version < "3.11"
Requires-Dist: pytest<9,>=8.2
Requires-Dist: typing-extensions>=4.12; python_version < "3.13"
Provides-Extra: docs
Requires-Dist: sphinx>=5.3; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1; extra == "docs"
Provides-Extra: testing
Requires-Dist: coverage>=6.2; extra == "testing"
Requires-Dist: hypothesis>=5.7.1; extra == "testing"
Dynamic: license-file

pytest-asyncio
==============

.. image:: https://img.shields.io/pypi/v/pytest-asyncio.svg
    :target: https://pypi.python.org/pypi/pytest-asyncio
.. image:: https://github.com/pytest-dev/pytest-asyncio/workflows/CI/badge.svg
    :target: https://github.com/pytest-dev/pytest-asyncio/actions?workflow=CI
.. image:: https://codecov.io/gh/pytest-dev/pytest-asyncio/branch/main/graph/badge.svg
    :target: https://codecov.io/gh/pytest-dev/pytest-asyncio
.. image:: https://img.shields.io/pypi/pyversions/pytest-asyncio.svg
    :target: https://github.com/pytest-dev/pytest-asyncio
    :alt: Supported Python versions
.. image:: https://img.shields.io/badge/Matrix-%23pytest--asyncio-brightgreen
    :alt: Matrix chat room: #pytest-asyncio
    :target: https://matrix.to/#/#pytest-asyncio:matrix.org

`pytest-asyncio <https://pytest-asyncio.readthedocs.io/en/latest/>`_ is a `pytest <https://docs.pytest.org/en/latest/contents.html>`_ plugin. It facilitates testing of code that uses the `asyncio <https://docs.python.org/3/library/asyncio.html>`_ library.

Specifically, pytest-asyncio provides support for coroutines as test functions. This allows users to *await* code inside their tests. For example, the following code is executed as a test item by pytest:

.. code-block:: python

    @pytest.mark.asyncio
    async def test_some_asyncio_code():
        res = await library.do_something()
        assert b"expected result" == res

More details can be found in the `documentation <https://pytest-asyncio.readthedocs.io/en/latest/>`_.

Note that test classes subclassing the standard `unittest <https://docs.python.org/3/library/unittest.html>`__ library are not supported. Users
are advised to use `unittest.IsolatedAsyncioTestCase <https://docs.python.org/3/library/unittest.html#unittest.IsolatedAsyncioTestCase>`__
or an async framework such as `asynctest <https://asynctest.readthedocs.io/en/latest>`__.


pytest-asyncio is available under the `Apache License 2.0 <https://github.com/pytest-dev/pytest-asyncio/blob/main/LICENSE>`_.


Installation
------------

To install pytest-asyncio, simply:

.. code-block:: bash

    $ pip install pytest-asyncio

This is enough for pytest to pick up pytest-asyncio.


Contributing
------------
Contributions are very welcome. Tests can be run with ``tox``, please ensure
the coverage at least stays the same before you submit a pull request.
